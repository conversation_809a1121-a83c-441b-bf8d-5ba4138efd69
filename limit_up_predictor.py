"""
涨停板预测器 - 基于昨日数据和今日开盘情况预测封板概率
"""

import pandas as pd
import numpy as np
import akshare as ak
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import time

logger = logging.getLogger(__name__)

class LimitUpPredictor:
    """涨停板预测器"""

    def __init__(self):
        self.cache_timeout = 300  # 5分钟缓存
        self.cache = {}

    def get_mainboard_stocks(self) -> pd.DataFrame:
        """获取主板股票列表（沪深主板，排除创业板、科创板）"""
        try:
            # 获取A股股票列表
            stock_list = ak.stock_info_a_code_name()

            if not stock_list.empty:
                # 筛选主板股票
                # 沪市主板: 600xxx, 601xxx, 603xxx, 605xxx
                # 深市主板: 000xxx, 001xxx, 002xxx (排除002开头的中小板)
                mainboard_stocks = stock_list[
                    (stock_list['code'].str.startswith('600')) |
                    (stock_list['code'].str.startswith('601')) |
                    (stock_list['code'].str.startswith('603')) |
                    (stock_list['code'].str.startswith('605')) |
                    (stock_list['code'].str.startswith('000')) |
                    (stock_list['code'].str.startswith('001'))
                ]

                logger.info(f"获取主板股票: {len(mainboard_stocks)} 只")
                return mainboard_stocks
            else:
                logger.warning("未获取到股票列表")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取主板股票列表失败: {e}")
            return pd.DataFrame()

    def analyze_yesterday_performance(self, stock_code: str) -> Dict:
        """分析昨日表现"""
        try:
            # 获取最近5天的历史数据
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=10)).strftime('%Y%m%d')

            hist_data = ak.stock_zh_a_hist(symbol=stock_code, period="daily",
                                         start_date=start_date, end_date=end_date)

            if len(hist_data) < 2:
                return {'error': '历史数据不足'}

            yesterday = hist_data.iloc[-2]  # 昨日数据
            day_before = hist_data.iloc[-3] if len(hist_data) >= 3 else yesterday

            analysis = {
                'stock_code': stock_code,
                'yesterday_close': float(yesterday['收盘']),
                'yesterday_high': float(yesterday['最高']),
                'yesterday_low': float(yesterday['最低']),
                'yesterday_open': float(yesterday['开盘']),
                'yesterday_volume': float(yesterday['成交量']),
                'yesterday_turnover': float(yesterday['成交额']),
                'yesterday_change_pct': float(yesterday['涨跌幅']),
                'yesterday_amplitude': float(yesterday['振幅']),
                'yesterday_turnover_rate': float(yesterday['换手率']),
            }

            # 计算昨日强度指标
            analysis['price_position'] = (yesterday['收盘'] - yesterday['最低']) / (yesterday['最高'] - yesterday['最低']) if yesterday['最高'] != yesterday['最低'] else 0.5
            analysis['volume_ratio'] = yesterday['成交量'] / day_before['成交量'] if day_before['成交量'] > 0 else 1

            # 昨日强度评分
            score = 0

            # 涨跌幅评分 (30%)
            if analysis['yesterday_change_pct'] > 7:
                score += 30
            elif analysis['yesterday_change_pct'] > 5:
                score += 25
            elif analysis['yesterday_change_pct'] > 3:
                score += 20
            elif analysis['yesterday_change_pct'] > 1:
                score += 15
            elif analysis['yesterday_change_pct'] > 0:
                score += 10

            # 收盘位置评分 (20%)
            if analysis['price_position'] > 0.8:
                score += 20
            elif analysis['price_position'] > 0.6:
                score += 15
            elif analysis['price_position'] > 0.4:
                score += 10

            # 成交量评分 (20%)
            if analysis['volume_ratio'] > 3:
                score += 20
            elif analysis['volume_ratio'] > 2:
                score += 15
            elif analysis['volume_ratio'] > 1.5:
                score += 10

            # 换手率评分 (15%)
            if analysis['yesterday_turnover_rate'] > 10:
                score += 15
            elif analysis['yesterday_turnover_rate'] > 5:
                score += 10
            elif analysis['yesterday_turnover_rate'] > 3:
                score += 5

            # 振幅评分 (15%)
            if 5 < analysis['yesterday_amplitude'] < 15:
                score += 15
            elif 3 < analysis['yesterday_amplitude'] < 20:
                score += 10

            analysis['yesterday_strength_score'] = score

            return analysis

        except Exception as e:
            logger.error(f"分析股票 {stock_code} 昨日表现失败: {e}")
            return {'error': str(e)}

    def analyze_today_opening(self, stock_code: str) -> Dict:
        """分析今日开盘情况"""
        try:
            # 获取实时数据
            realtime_data = ak.stock_zh_a_spot_em()

            if realtime_data.empty:
                return {'error': '未获取到实时数据'}

            # 查找目标股票
            stock_data = realtime_data[realtime_data['代码'] == stock_code]

            if stock_data.empty:
                return {'error': f'未找到股票 {stock_code} 的实时数据'}

            stock = stock_data.iloc[0]

            analysis = {
                'stock_code': stock_code,
                'current_price': float(stock['最新价']),
                'open_price': float(stock['今开']),
                'high_price': float(stock['最高']),
                'low_price': float(stock['最低']),
                'yesterday_close': float(stock['昨收']),
                'current_change_pct': float(stock['涨跌幅']),
                'current_volume': float(stock['成交量']),
                'current_turnover': float(stock['成交额']),
                'current_turnover_rate': float(stock['换手率']),
            }

            # 计算开盘强度指标
            analysis['gap_pct'] = (analysis['open_price'] - analysis['yesterday_close']) / analysis['yesterday_close'] * 100
            analysis['intraday_strength'] = (analysis['current_price'] - analysis['low_price']) / (analysis['high_price'] - analysis['low_price']) if analysis['high_price'] != analysis['low_price'] else 0.5

            # 今日开盘强度评分
            score = 0

            # 高开幅度评分 (25%)
            if analysis['gap_pct'] > 5:
                score += 25
            elif analysis['gap_pct'] > 3:
                score += 20
            elif analysis['gap_pct'] > 1:
                score += 15
            elif analysis['gap_pct'] > 0:
                score += 10

            # 当前涨幅评分 (25%)
            if analysis['current_change_pct'] > 8:
                score += 25
            elif analysis['current_change_pct'] > 6:
                score += 20
            elif analysis['current_change_pct'] > 4:
                score += 15
            elif analysis['current_change_pct'] > 2:
                score += 10

            # 盘中强度评分 (20%)
            if analysis['intraday_strength'] > 0.8:
                score += 20
            elif analysis['intraday_strength'] > 0.6:
                score += 15
            elif analysis['intraday_strength'] > 0.4:
                score += 10

            # 成交活跃度评分 (15%)
            if analysis['current_turnover_rate'] > 8:
                score += 15
            elif analysis['current_turnover_rate'] > 5:
                score += 10
            elif analysis['current_turnover_rate'] > 3:
                score += 5

            # 价格位置评分 (15%)
            limit_up_price = analysis['yesterday_close'] * 1.1  # 涨停价
            distance_to_limit = (limit_up_price - analysis['current_price']) / analysis['yesterday_close'] * 100

            if distance_to_limit < 1:
                score += 15
            elif distance_to_limit < 2:
                score += 12
            elif distance_to_limit < 3:
                score += 8
            elif distance_to_limit < 5:
                score += 5

            analysis['today_opening_score'] = score
            analysis['distance_to_limit_up'] = distance_to_limit

            return analysis

        except Exception as e:
            logger.error(f"分析股票 {stock_code} 今日开盘情况失败: {e}")
            return {'error': str(e)}

    def get_fund_flow_strength(self, stock_code: str) -> Dict:
        """获取资金流向强度"""
        try:
            # 获取个股资金流向
            fund_flow = ak.stock_individual_fund_flow(stock=stock_code, market='sz' if stock_code.startswith('0') else 'sh')

            if fund_flow.empty:
                return {'fund_flow_score': 0, 'main_fund_inflow': 0}

            latest_flow = fund_flow.iloc[-1]

            analysis = {
                'main_fund_inflow': float(latest_flow.get('主力净流入', 0)),
                'main_fund_inflow_pct': float(latest_flow.get('主力净流入占比', 0)),
                'super_large_inflow': float(latest_flow.get('超大单净流入', 0)),
                'large_inflow': float(latest_flow.get('大单净流入', 0)),
            }

            # 资金流向评分
            score = 0

            if analysis['main_fund_inflow'] > 1e8:  # 超过1亿
                score += 25
            elif analysis['main_fund_inflow'] > 5e7:  # 超过5000万
                score += 20
            elif analysis['main_fund_inflow'] > 2e7:  # 超过2000万
                score += 15
            elif analysis['main_fund_inflow'] > 0:
                score += 10

            if analysis['main_fund_inflow_pct'] > 10:
                score += 15
            elif analysis['main_fund_inflow_pct'] > 5:
                score += 10
            elif analysis['main_fund_inflow_pct'] > 0:
                score += 5

            analysis['fund_flow_score'] = score

            return analysis

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 资金流向失败: {e}")
            return {'fund_flow_score': 0, 'main_fund_inflow': 0}

    def calculate_limit_up_probability(self, stock_code: str) -> Dict:
        """计算涨停概率"""
        try:
            # 获取各项分析数据
            yesterday_analysis = self.analyze_yesterday_performance(stock_code)
            today_analysis = self.analyze_today_opening(stock_code)
            fund_analysis = self.get_fund_flow_strength(stock_code)

            if 'error' in yesterday_analysis or 'error' in today_analysis:
                return {'error': '数据获取失败'}

            # 综合评分计算
            yesterday_score = yesterday_analysis.get('yesterday_strength_score', 0)
            today_score = today_analysis.get('today_opening_score', 0)
            fund_score = fund_analysis.get('fund_flow_score', 0)

            # 权重分配
            total_score = (
                yesterday_score * 0.3 +  # 昨日表现 30%
                today_score * 0.5 +      # 今日开盘 50%
                fund_score * 0.2         # 资金流向 20%
            )

            # 转换为概率 (0-100%)
            probability = min(total_score, 100)

            # 风险等级
            if probability >= 80:
                risk_level = "极高概率"
                recommendation = "重点关注，可能封板"
            elif probability >= 65:
                risk_level = "高概率"
                recommendation = "积极关注，有封板可能"
            elif probability >= 50:
                risk_level = "中等概率"
                recommendation = "谨慎关注，存在机会"
            elif probability >= 35:
                risk_level = "较低概率"
                recommendation = "观望为主"
            else:
                risk_level = "低概率"
                recommendation = "不建议关注"

            result = {
                'stock_code': stock_code,
                'stock_name': today_analysis.get('stock_name', ''),
                'limit_up_probability': probability,
                'risk_level': risk_level,
                'recommendation': recommendation,
                'current_price': today_analysis.get('current_price', 0),
                'current_change_pct': today_analysis.get('current_change_pct', 0),
                'distance_to_limit_up': today_analysis.get('distance_to_limit_up', 0),
                'yesterday_score': yesterday_score,
                'today_score': today_score,
                'fund_score': fund_score,
                'total_score': total_score,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'detailed_analysis': {
                    'yesterday': yesterday_analysis,
                    'today': today_analysis,
                    'fund_flow': fund_analysis
                }
            }

            return result

        except Exception as e:
            logger.error(f"计算股票 {stock_code} 涨停概率失败: {e}")
            return {'error': str(e)}

    def scan_potential_limit_up_stocks(self, industry_filter: List[str] = None, top_n: int = 20) -> pd.DataFrame:
        """扫描潜在涨停股票"""
        try:
            # 获取今日涨幅较大的股票作为候选
            realtime_data = ak.stock_zh_a_spot_em()

            if realtime_data.empty:
                logger.warning("未获取到实时数据")
                return pd.DataFrame()

            # 筛选主板股票
            mainboard_data = realtime_data[
                (realtime_data['代码'].str.startswith('600')) |
                (realtime_data['代码'].str.startswith('601')) |
                (realtime_data['代码'].str.startswith('603')) |
                (realtime_data['代码'].str.startswith('605')) |
                (realtime_data['代码'].str.startswith('000')) |
                (realtime_data['代码'].str.startswith('001'))
            ]

            # 筛选条件：涨幅>2%，成交额>5000万，换手率>1%
            candidates = mainboard_data[
                (mainboard_data['涨跌幅'] > 2) &
                (mainboard_data['涨跌幅'] < 9.5) &  # 排除已经接近涨停的
                (mainboard_data['成交额'] > 5e7) &
                (mainboard_data['换手率'] > 1) &
                (mainboard_data['最新价'] > 3)  # 排除低价股
            ].copy()

            if candidates.empty:
                logger.warning("未找到符合条件的候选股票")
                return pd.DataFrame()

            # 按涨幅排序，选择前50只进行详细分析
            candidates = candidates.sort_values('涨跌幅', ascending=False).head(50)

            results = []

            for _, stock in candidates.iterrows():
                try:
                    stock_code = stock['代码']

                    # 计算涨停概率
                    prediction = self.calculate_limit_up_probability(stock_code)

                    if 'error' not in prediction:
                        prediction['stock_name'] = stock['名称']
                        prediction['industry'] = stock.get('所属行业', '未知')
                        results.append(prediction)

                    # 避免请求过快
                    time.sleep(0.1)

                except Exception as e:
                    logger.warning(f"分析股票 {stock['代码']} 失败: {e}")
                    continue

            if results:
                # 转换为DataFrame并排序
                results_df = pd.DataFrame(results)
                results_df = results_df.sort_values('limit_up_probability', ascending=False).head(top_n)

                logger.info(f"成功分析 {len(results_df)} 只潜在涨停股票")
                return results_df
            else:
                logger.warning("未找到有效的分析结果")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"扫描潜在涨停股票失败: {e}")
            return pd.DataFrame()

    def get_industry_limit_up_potential(self, industry_name: str) -> Dict:
        """获取行业涨停潜力分析"""
        try:
            # 获取行业成分股
            from data_fetcher import DataFetcher
            data_fetcher = DataFetcher()

            industry_stocks = data_fetcher.get_industry_stocks(industry_name, top_n=30)

            if industry_stocks.empty:
                return {'error': f'未获取到行业 {industry_name} 的成分股'}

            # 筛选主板股票
            mainboard_stocks = industry_stocks[
                (industry_stocks['代码'].str.startswith('600')) |
                (industry_stocks['代码'].str.startswith('601')) |
                (industry_stocks['代码'].str.startswith('603')) |
                (industry_stocks['代码'].str.startswith('605')) |
                (industry_stocks['代码'].str.startswith('000')) |
                (industry_stocks['代码'].str.startswith('001'))
            ]

            if mainboard_stocks.empty:
                return {'error': f'行业 {industry_name} 无主板股票'}

            predictions = []

            for _, stock in mainboard_stocks.head(10).iterrows():  # 分析前10只
                try:
                    prediction = self.calculate_limit_up_probability(stock['代码'])
                    if 'error' not in prediction:
                        prediction['stock_name'] = stock['名称']
                        predictions.append(prediction)
                    time.sleep(0.1)
                except:
                    continue

            if predictions:
                # 计算行业整体潜力
                avg_probability = np.mean([p['limit_up_probability'] for p in predictions])
                high_potential_count = len([p for p in predictions if p['limit_up_probability'] >= 60])

                # 找出最有潜力的股票
                best_stock = max(predictions, key=lambda x: x['limit_up_probability'])

                return {
                    'industry_name': industry_name,
                    'analyzed_stocks_count': len(predictions),
                    'average_probability': avg_probability,
                    'high_potential_count': high_potential_count,
                    'best_stock': best_stock,
                    'all_predictions': predictions,
                    'industry_rating': self._get_industry_rating(avg_probability, high_potential_count)
                }
            else:
                return {'error': f'行业 {industry_name} 分析失败'}

        except Exception as e:
            logger.error(f"分析行业 {industry_name} 涨停潜力失败: {e}")
            return {'error': str(e)}

    def _get_industry_rating(self, avg_probability: float, high_potential_count: int) -> str:
        """获取行业评级"""
        if avg_probability >= 60 and high_potential_count >= 3:
            return "🔥 极强"
        elif avg_probability >= 50 and high_potential_count >= 2:
            return "⭐ 强势"
        elif avg_probability >= 40 and high_potential_count >= 1:
            return "📈 中等"
        elif avg_probability >= 30:
            return "📊 一般"
        else:
            return "📉 较弱"
