"""
验证行业数据的动态性
"""

from data_fetcher import DataFetcher
import pandas as pd
from datetime import datetime

def verify_dynamic_industries():
    """验证行业数据的动态性"""
    print('🔍 验证行业数据的动态性')
    print('='*50)
    print(f'⏰ 分析时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    fetcher = DataFetcher()
    industry_data = fetcher.get_industry_data()
    
    if 'sw_industry' in industry_data:
        df = industry_data['sw_industry']
        print(f'\n📊 实时获取到 {len(df)} 个动态行业板块')
        
        print('\n📋 所有行业名称 (前30个):')
        for i, industry in enumerate(df['板块名称'].head(30), 1):
            print(f'  {i:2d}. {industry}')
        
        # 按涨跌幅排序
        df_sorted = df.sort_values('涨跌幅', ascending=False)
        
        print('\n📈 今日涨幅前15的行业:')
        for i, (_, row) in enumerate(df_sorted.head(15).iterrows(), 1):
            industry_name = row['板块名称']
            change_pct = row['涨跌幅']
            print(f'  {i:2d}. {industry_name:<15} {change_pct:+6.2f}%')
        
        print('\n📉 今日跌幅前15的行业:')
        for i, (_, row) in enumerate(df_sorted.tail(15).iterrows(), 1):
            industry_name = row['板块名称']
            change_pct = row['涨跌幅']
            print(f'  {i:2d}. {industry_name:<15} {change_pct:+6.2f}%')
        
        # 显示数据的实时性
        print(f'\n🔄 数据特点:')
        print(f'  • 行业数量: {len(df)} 个 (动态获取)')
        print(f'  • 数据来源: 东方财富实时数据')
        print(f'  • 更新频率: 交易时间内实时更新')
        print(f'  • 涨跌幅范围: {df["涨跌幅"].min():.2f}% 到 {df["涨跌幅"].max():.2f}%')
        
        return df
    else:
        print('❌ 未获取到行业数据')
        return None

def show_industry_changes():
    """展示行业变化的动态性"""
    print('\n🔄 行业轮动的动态特征')
    print('='*50)
    
    from industry_analyzer import IndustryAnalyzer
    analyzer = IndustryAnalyzer()
    
    # 获取当前行业表现
    performance_df = analyzer.calculate_industry_performance()
    
    if not performance_df.empty:
        # 检测轮动信号
        signals = analyzer.detect_rotation_signals(performance_df)
        
        print('📊 当前市场轮动状态:')
        print(f'  🟢 强势行业: {len(signals.get("strong_industries", []))} 个')
        print(f'  🔴 弱势行业: {len(signals.get("weak_industries", []))} 个')
        print(f'  🔄 轮动候选: {len(signals.get("rotation_candidates", []))} 个')
        print(f'  🛡️ 防御性行业: {len(signals.get("defensive_industries", []))} 个')
        
        print('\n🎯 强势行业详情:')
        for i, industry in enumerate(signals.get('strong_industries', [])[:10], 1):
            print(f'  {i:2d}. {industry}')
        
        print('\n💡 系统动态特性:')
        print('  • 每次运行都会获取最新的市场数据')
        print('  • 行业排名会根据实时涨跌幅变化')
        print('  • 强势/弱势行业会动态调整')
        print('  • 轮动信号会根据市场变化实时更新')

def compare_with_static():
    """对比静态配置和动态数据"""
    print('\n🔍 静态配置 vs 动态数据对比')
    print('='*50)
    
    # 静态配置的行业
    from config import INDUSTRY_MAPPING
    static_industries = list(INDUSTRY_MAPPING.keys())
    
    # 动态获取的行业
    fetcher = DataFetcher()
    industry_data = fetcher.get_industry_data()
    
    if 'sw_industry' in industry_data:
        dynamic_industries = industry_data['sw_industry']['板块名称'].tolist()
        
        print(f'📋 静态配置行业 ({len(static_industries)}个):')
        for i, industry in enumerate(static_industries, 1):
            print(f'  {i}. {industry}')
        
        print(f'\n📊 动态获取行业 (前{len(static_industries)}个，共{len(dynamic_industries)}个):')
        for i, industry in enumerate(dynamic_industries[:len(static_industries)], 1):
            print(f'  {i}. {industry}')
        
        print(f'\n🔄 差异分析:')
        print(f'  • 静态配置: {len(static_industries)} 个固定行业分类')
        print(f'  • 动态获取: {len(dynamic_industries)} 个实时行业板块')
        print(f'  • 数据丰富度: 动态数据是静态的 {len(dynamic_industries)/len(static_industries):.1f} 倍')
        print(f'  • 实时性: 动态数据每次都是最新的市场状态')

if __name__ == "__main__":
    # 验证动态性
    df = verify_dynamic_industries()
    
    if df is not None:
        # 展示轮动动态性
        show_industry_changes()
        
        # 对比静态和动态
        compare_with_static()
        
        print('\n🎉 结论: 系统完全基于动态数据，每次分析都是最新的市场状态！')
    else:
        print('❌ 无法验证动态性，请检查网络连接')
