"""
技术分析模块 - 股票数据分析系统
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
from data_fetcher import DataFetcher
from config import *

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class TechnicalAnalyzer:
    """技术分析类"""
    
    def __init__(self):
        self.data_fetcher = DataFetcher()
    
    def calculate_ma(self, data: pd.Series, period: int) -> pd.Series:
        """计算移动平均线"""
        return data.rolling(window=period).mean()
    
    def calculate_rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """计算MACD指标"""
        ema_fast = data.ewm(span=fast).mean()
        ema_slow = data.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def calculate_bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: int = 2) -> Dict[str, pd.Series]:
        """计算布林带"""
        ma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()
        upper_band = ma + (std * std_dev)
        lower_band = ma - (std * std_dev)
        
        return {
            'upper': upper_band,
            'middle': ma,
            'lower': lower_band
        }
    
    def calculate_kdj(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 9) -> Dict[str, pd.Series]:
        """计算KDJ指标"""
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
        k = rsv.ewm(com=2).mean()
        d = k.ewm(com=2).mean()
        j = 3 * k - 2 * d
        
        return {
            'k': k,
            'd': d,
            'j': j
        }
    
    def analyze_stock_technical(self, symbol: str) -> Dict:
        """分析单只股票的技术指标"""
        try:
            # 获取股票数据
            stock_data = self.data_fetcher.get_stock_price_data(symbol)
            
            if stock_data.empty:
                logger.warning(f"未获取到股票 {symbol} 的数据")
                return {}
            
            analysis_result = {
                'symbol': symbol,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'current_price': float(stock_data['Close'].iloc[-1]),
                'indicators': {},
                'signals': {},
                'support_resistance': {}
            }
            
            # 计算技术指标
            close_price = stock_data['Close']
            high_price = stock_data['High']
            low_price = stock_data['Low']
            volume = stock_data['Volume']
            
            # 移动平均线
            for period in TECHNICAL_INDICATORS['ma_periods']:
                ma_key = f'MA{period}'
                analysis_result['indicators'][ma_key] = self.calculate_ma(close_price, period).iloc[-1]
            
            # RSI
            rsi = self.calculate_rsi(close_price, TECHNICAL_INDICATORS['rsi_period'])
            analysis_result['indicators']['RSI'] = rsi.iloc[-1]
            
            # MACD
            macd_data = self.calculate_macd(close_price, *TECHNICAL_INDICATORS['macd_params'])
            analysis_result['indicators']['MACD'] = macd_data['macd'].iloc[-1]
            analysis_result['indicators']['MACD_Signal'] = macd_data['signal'].iloc[-1]
            analysis_result['indicators']['MACD_Histogram'] = macd_data['histogram'].iloc[-1]
            
            # 布林带
            bollinger = self.calculate_bollinger_bands(close_price, TECHNICAL_INDICATORS['bollinger_period'])
            analysis_result['indicators']['BB_Upper'] = bollinger['upper'].iloc[-1]
            analysis_result['indicators']['BB_Middle'] = bollinger['middle'].iloc[-1]
            analysis_result['indicators']['BB_Lower'] = bollinger['lower'].iloc[-1]
            
            # KDJ
            kdj = self.calculate_kdj(high_price, low_price, close_price)
            analysis_result['indicators']['K'] = kdj['k'].iloc[-1]
            analysis_result['indicators']['D'] = kdj['d'].iloc[-1]
            analysis_result['indicators']['J'] = kdj['j'].iloc[-1]
            
            # 成交量指标
            volume_ma = self.calculate_ma(volume, TECHNICAL_INDICATORS['volume_ma_period'])
            analysis_result['indicators']['Volume_MA'] = volume_ma.iloc[-1]
            analysis_result['indicators']['Volume_Ratio'] = volume.iloc[-1] / volume_ma.iloc[-1] if volume_ma.iloc[-1] > 0 else 0
            
            # 生成交易信号
            signals = self._generate_trading_signals(analysis_result['indicators'], close_price)
            analysis_result['signals'] = signals
            
            # 计算支撑阻力位
            support_resistance = self._calculate_support_resistance(stock_data)
            analysis_result['support_resistance'] = support_resistance
            
            logger.info(f"股票 {symbol} 技术分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析股票 {symbol} 技术指标失败: {e}")
            return {}
    
    def _generate_trading_signals(self, indicators: Dict, price_series: pd.Series) -> Dict[str, str]:
        """生成交易信号"""
        signals = {
            'ma_signal': 'neutral',
            'rsi_signal': 'neutral',
            'macd_signal': 'neutral',
            'bb_signal': 'neutral',
            'kdj_signal': 'neutral',
            'volume_signal': 'neutral',
            'overall_signal': 'neutral'
        }
        
        try:
            current_price = price_series.iloc[-1]
            
            # MA信号
            if current_price > indicators.get('MA5', 0) > indicators.get('MA10', 0) > indicators.get('MA20', 0):
                signals['ma_signal'] = 'bullish'
            elif current_price < indicators.get('MA5', 0) < indicators.get('MA10', 0) < indicators.get('MA20', 0):
                signals['ma_signal'] = 'bearish'
            
            # RSI信号
            rsi = indicators.get('RSI', 50)
            if rsi > 70:
                signals['rsi_signal'] = 'overbought'
            elif rsi < 30:
                signals['rsi_signal'] = 'oversold'
            elif 30 <= rsi <= 70:
                signals['rsi_signal'] = 'neutral'
            
            # MACD信号
            macd = indicators.get('MACD', 0)
            macd_signal = indicators.get('MACD_Signal', 0)
            if macd > macd_signal and macd > 0:
                signals['macd_signal'] = 'bullish'
            elif macd < macd_signal and macd < 0:
                signals['macd_signal'] = 'bearish'
            
            # 布林带信号
            bb_upper = indicators.get('BB_Upper', 0)
            bb_lower = indicators.get('BB_Lower', 0)
            if current_price > bb_upper:
                signals['bb_signal'] = 'overbought'
            elif current_price < bb_lower:
                signals['bb_signal'] = 'oversold'
            
            # KDJ信号
            k = indicators.get('K', 50)
            d = indicators.get('D', 50)
            if k > 80 and d > 80:
                signals['kdj_signal'] = 'overbought'
            elif k < 20 and d < 20:
                signals['kdj_signal'] = 'oversold'
            elif k > d:
                signals['kdj_signal'] = 'bullish'
            elif k < d:
                signals['kdj_signal'] = 'bearish'
            
            # 成交量信号
            volume_ratio = indicators.get('Volume_Ratio', 1)
            if volume_ratio > 2:
                signals['volume_signal'] = 'high_volume'
            elif volume_ratio < 0.5:
                signals['volume_signal'] = 'low_volume'
            
            # 综合信号
            bullish_count = sum(1 for signal in signals.values() if signal == 'bullish')
            bearish_count = sum(1 for signal in signals.values() if signal == 'bearish')
            
            if bullish_count >= 3:
                signals['overall_signal'] = 'bullish'
            elif bearish_count >= 3:
                signals['overall_signal'] = 'bearish'
            
        except Exception as e:
            logger.warning(f"生成交易信号失败: {e}")
        
        return signals
    
    def _calculate_support_resistance(self, stock_data: pd.DataFrame) -> Dict:
        """计算支撑阻力位"""
        try:
            high_prices = stock_data['High'].tail(60)  # 最近60天
            low_prices = stock_data['Low'].tail(60)
            
            # 简单的支撑阻力位计算
            resistance_levels = []
            support_levels = []
            
            # 找出局部高点和低点
            for i in range(2, len(high_prices) - 2):
                if (high_prices.iloc[i] > high_prices.iloc[i-1] and 
                    high_prices.iloc[i] > high_prices.iloc[i+1] and
                    high_prices.iloc[i] > high_prices.iloc[i-2] and 
                    high_prices.iloc[i] > high_prices.iloc[i+2]):
                    resistance_levels.append(high_prices.iloc[i])
                
                if (low_prices.iloc[i] < low_prices.iloc[i-1] and 
                    low_prices.iloc[i] < low_prices.iloc[i+1] and
                    low_prices.iloc[i] < low_prices.iloc[i-2] and 
                    low_prices.iloc[i] < low_prices.iloc[i+2]):
                    support_levels.append(low_prices.iloc[i])
            
            return {
                'resistance_levels': sorted(resistance_levels, reverse=True)[:3],
                'support_levels': sorted(support_levels, reverse=True)[:3]
            }
            
        except Exception as e:
            logger.warning(f"计算支撑阻力位失败: {e}")
            return {'resistance_levels': [], 'support_levels': []}
