"""
检查其他功能模块
"""

def check_policy_analysis():
    """检查政策影响分析"""
    print("🔍 检查政策影响分析功能...")
    try:
        from industry_analyzer import IndustryAnalyzer
        analyzer = IndustryAnalyzer()
        
        policy_impact = analyzer.analyze_policy_impact(['新能源', '人工智能', '医药'])
        print(f"   ✅ 政策影响分析: {len(policy_impact)}个关键词")
        
        for keyword, impact in policy_impact.items():
            news_count = impact.get('news_count', 0)
            impact_score = impact.get('impact_score', 0)
            affected_industries = impact.get('affected_industries', [])
            print(f"   📰 {keyword}: {news_count}条新闻, 影响评分{impact_score:.2f}, 影响{len(affected_industries)}个行业")
        
        return True
    except Exception as e:
        print(f"   ❌ 政策影响分析失败: {e}")
        return False

def check_market_sentiment():
    """检查市场情绪指标"""
    print("\n🔍 检查市场情绪指标...")
    try:
        from data_fetcher import DataFetcher
        fetcher = DataFetcher()
        
        sentiment_data = fetcher.get_market_sentiment()
        print(f"   ✅ 市场情绪数据: {len(sentiment_data)}类指标")
        
        for key, data in sentiment_data.items():
            if hasattr(data, '__len__'):
                print(f"   📊 {key}: {len(data)}条记录")
            else:
                print(f"   📊 {key}: 数据获取成功")
        
        return True
    except Exception as e:
        print(f"   ❌ 市场情绪指标失败: {e}")
        return False

def check_financial_analysis():
    """检查财务数据分析"""
    print("\n🔍 检查财务数据分析...")
    try:
        from data_fetcher import DataFetcher
        fetcher = DataFetcher()
        
        # 测试获取财务数据
        financial_data = fetcher.get_financial_data('000001')
        if not financial_data.empty:
            print(f"   ✅ 财务数据: 获取{len(financial_data)}项财务指标")
        else:
            print("   ⚠️ 财务数据: 未获取到数据")
        
        return True
    except Exception as e:
        print(f"   ❌ 财务数据分析失败: {e}")
        return False

def check_visualization():
    """检查可视化功能"""
    print("\n🔍 检查可视化功能...")
    try:
        from industry_analyzer import IndustryAnalyzer
        import matplotlib.pyplot as plt
        
        analyzer = IndustryAnalyzer()
        performance_df = analyzer.calculate_industry_performance()
        
        if not performance_df.empty:
            # 测试绘图功能（不显示）
            plt.ioff()  # 关闭交互模式
            analyzer.plot_industry_performance(performance_df, save_path='test_plot.png')
            plt.close('all')  # 关闭所有图形
            
            print("   ✅ 行业表现图表: 生成成功")
            
            # 检查是否生成了文件
            import os
            if os.path.exists('test_plot.png'):
                print("   ✅ 图表文件: 保存成功")
                os.remove('test_plot.png')  # 清理测试文件
            
        return True
    except Exception as e:
        print(f"   ❌ 可视化功能失败: {e}")
        return False

def check_backtest_visualization():
    """检查回测可视化"""
    print("\n🔍 检查回测可视化功能...")
    try:
        from backtest_engine import BacktestEngine, ma_crossover_strategy
        import matplotlib.pyplot as plt
        
        engine = BacktestEngine(initial_capital=100000)
        engine.add_strategy(ma_crossover_strategy, "测试策略")
        
        # 运行简短回测
        results = engine.run_backtest(
            symbols=['000001'],
            start_date='2024-12-01',
            end_date='2024-12-31',
            strategy_params={'short_period': 5, 'long_period': 20}
        )
        
        if results:
            # 测试回测结果绘图
            plt.ioff()  # 关闭交互模式
            engine.plot_backtest_results(results, save_path='test_backtest.png')
            plt.close('all')  # 关闭所有图形
            
            print("   ✅ 回测结果图表: 生成成功")
            
            # 检查文件
            import os
            if os.path.exists('test_backtest.png'):
                print("   ✅ 回测图表文件: 保存成功")
                os.remove('test_backtest.png')  # 清理测试文件
        
        return True
    except Exception as e:
        print(f"   ❌ 回测可视化失败: {e}")
        return False

def check_web_app_components():
    """检查Web应用组件"""
    print("\n🔍 检查Web应用组件...")
    try:
        # 检查主要页面函数
        from main_app import industry_rotation_page, technical_analysis_page, backtest_page
        print("   ✅ 行业轮动页面: 导入成功")
        print("   ✅ 技术分析页面: 导入成功") 
        print("   ✅ 策略回测页面: 导入成功")
        
        # 检查配置文件
        import config
        print(f"   ✅ 配置文件: {len(config.INDUSTRY_MAPPING)}个行业映射")
        print(f"   ✅ 龙头股票: {len(config.LEADING_STOCKS)}个行业配置")
        
        return True
    except Exception as e:
        print(f"   ❌ Web应用组件失败: {e}")
        return False

def check_error_handling():
    """检查错误处理"""
    print("\n🔍 检查错误处理机制...")
    try:
        from technical_analyzer import TechnicalAnalyzer
        from data_fetcher import DataFetcher
        
        # 测试无效股票代码
        analyzer = TechnicalAnalyzer()
        result = analyzer.analyze_stock_technical('INVALID')
        if not result:
            print("   ✅ 无效股票代码: 错误处理正常")
        
        # 测试无效行业名称
        fetcher = DataFetcher()
        stocks = fetcher.get_industry_stocks('无效行业名称')
        if stocks.empty:
            print("   ✅ 无效行业名称: 错误处理正常")
        
        return True
    except Exception as e:
        print(f"   ❌ 错误处理检查失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🔍 其他功能模块全面检查")
    print("=" * 50)
    
    checks = [
        ("政策影响分析", check_policy_analysis),
        ("市场情绪指标", check_market_sentiment),
        ("财务数据分析", check_financial_analysis),
        ("可视化功能", check_visualization),
        ("回测可视化", check_backtest_visualization),
        ("Web应用组件", check_web_app_components),
        ("错误处理机制", check_error_handling)
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"   ❌ {name}检查异常: {e}")
            results[name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 其他功能检查结果")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for name, status in results.items():
        status_text = "✅ 正常" if status else "❌ 异常"
        print(f"{name:<12} {status_text}")
    
    print(f"\n📈 其他功能状态: {passed}/{total} 正常 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有其他功能都正常工作！")
    elif passed >= total * 0.8:
        print("⚠️ 大部分其他功能正常。")
    else:
        print("❌ 多个其他功能存在问题。")

if __name__ == "__main__":
    main()
