# 🚀 A股行业轮动分析系统 - 完整操作手册

## 📋 目录
1. [快速开始](#快速开始)
2. [Web界面操作](#web界面操作)
3. [命令行操作](#命令行操作)
4. [功能详解](#功能详解)
5. [常见问题](#常见问题)
6. [高级配置](#高级配置)

## 🚀 快速开始

### 环境检查
```bash
# 检查系统是否正常
python test_system.py
```

### 启动方式

#### 方式一：Web界面（推荐新手）
```bash
streamlit run main_app.py
```
- 浏览器访问：http://localhost:8501
- 图形化界面，操作简单
- 支持交互式图表

#### 方式二：命令行（推荐高级用户）
```bash
# 完整分析
python run_analysis.py

# 快速分析
python run_analysis.py quick

# 指定股票分析
python run_analysis.py stocks 000001,600519
```

## 🌐 Web界面操作

### 1. 行业轮动分析
**操作步骤：**
1. 选择"行业轮动分析"页面
2. 点击"🔍 分析当前行业轮动"按钮
3. 等待分析完成（约30-60秒）

**结果解读：**
- **行业表现排行**：显示各行业涨跌幅排名
- **轮动信号**：
  - 🟢 强势行业：资金流入，上涨动能强
  - 🔴 弱势行业：资金流出，下跌压力大
  - 🔄 轮动候选：可能成为下轮热点
  - 🛡️ 防御性行业：波动较小，相对稳定
- **强势行业股票**：每个强势行业的具体投资标的

### 2. 技术指标分析
**操作步骤：**
1. 选择"技术指标分析"页面
2. 输入6位股票代码（如：000001）
3. 点击"🔍 开始分析"

**结果解读：**
- **基本信息**：当前价格、综合信号
- **技术指标**：MA、RSI、MACD、布林带等
- **交易信号**：
  - 🟢 bullish：看涨信号
  - 🔴 bearish：看跌信号
  - 🟡 neutral：中性信号
- **支撑阻力位**：关键价位参考

### 3. 策略回测
**操作步骤：**
1. 选择"策略回测"页面
2. 输入股票代码列表（每行一个）
3. 设置回测参数：
   - 初始资金
   - 开始/结束日期
   - 策略参数（如MA周期）
4. 点击"🚀 开始回测"

**结果解读：**
- **关键指标**：总收益率、年化收益率、夏普比率、最大回撤
- **收益曲线**：可视化投资表现
- **交易记录**：详细的买卖记录
- **统计数据**：胜率、平均盈利等

## 💻 命令行操作

### 基本命令

```bash
# 1. 完整分析（推荐）
python run_analysis.py
# 输出：行业轮动分析 + 强势行业股票 + 技术分析

# 2. 快速行业分析
python run_analysis.py quick
# 输出：当前行业表现排行 + 轮动信号

# 3. 指定股票技术分析
python run_analysis.py stocks 000001,600519,300750
# 输出：指定股票的技术指标分析
```

### 高级命令

```bash
# 系统测试
python test_system.py

# 数据结构调试
python debug_data.py

# 直接启动Streamlit
python -m streamlit run main_app.py --server.port 8501
```

## 🔧 功能详解

### 行业轮动分析算法

**强度评分计算：**
```
强度评分 = 涨跌幅×30% + 成交额排名×20% + 市值排名×20% + 换手率排名×15% + 上涨股票比例×15%
```

**信号分类：**
- 强势行业：强度评分 ≥ 75分位数
- 弱势行业：强度评分 ≤ 25分位数
- 轮动候选：涨幅0-3%且强度评分>中位数
- 防御性行业：涨跌幅在-1%到1%之间

### 技术指标说明

| 指标 | 说明 | 信号解读 |
|------|------|----------|
| MA | 移动平均线 | 价格>MA看涨，价格<MA看跌 |
| RSI | 相对强弱指数 | >70超买，<30超卖 |
| MACD | 指数平滑移动平均 | 金叉看涨，死叉看跌 |
| 布林带 | 价格通道 | 突破上轨超买，跌破下轨超卖 |
| KDJ | 随机指标 | K>D看涨，K<D看跌 |

### 投资建议生成逻辑

1. **强势行业推荐**：基于强度评分排名
2. **轮动机会识别**：寻找即将轮动的行业
3. **政策受益分析**：结合政策新闻影响
4. **防御配置建议**：市场不确定时的选择

## ❓ 常见问题

### Q1: 数据获取失败怎么办？
**A:** 
```bash
# 检查网络连接
ping baidu.com

# 重新安装akshare
pip install --upgrade akshare

# 检查系统状态
python test_system.py
```

### Q2: 某些股票代码无法分析？
**A:** 
- 确保使用6位A股代码（如000001，不是1）
- 检查股票是否停牌
- 新股可能数据不足

### Q3: Web界面无法访问？
**A:**
```bash
# 检查端口是否被占用
netstat -an | findstr 8501

# 尝试其他端口
streamlit run main_app.py --server.port 8502

# 检查防火墙设置
```

### Q4: 分析结果不准确？
**A:**
- 数据有延迟是正常的（几分钟）
- 非交易时间显示上一交易日数据
- 建议结合多个信息源验证

### Q5: 如何获取更多股票？
**A:**
- 系统自动获取行业成分股（通常8-10只）
- 可在config.py中添加更多龙头股票
- 支持自定义行业分类

## ⚙️ 高级配置

### 自定义行业分类
编辑 `config.py`：
```python
INDUSTRY_MAPPING = {
    '新能源': ['新能源汽车', '光伏', '风电', '储能'],
    '人工智能': ['AI芯片', '算力', '软件'],
    # 添加更多行业...
}
```

### 添加龙头股票
```python
LEADING_STOCKS = {
    '新能源': {
        '比亚迪': '002594',
        '宁德时代': '300750',
        # 添加更多股票...
    }
}
```

### 调整技术指标参数
```python
TECHNICAL_INDICATORS = {
    'ma_periods': [5, 10, 20, 60],  # MA周期
    'rsi_period': 14,               # RSI周期
    'macd_params': (12, 26, 9),     # MACD参数
}
```

### 风险控制设置
```python
RISK_CONTROL = {
    'max_position': 0.1,    # 单股最大仓位10%
    'stop_loss': 0.08,      # 止损8%
    'take_profit': 0.15,    # 止盈15%
}
```

## 📊 输出文件说明

### 生成的文件
- `industry_performance.png`：行业表现图表
- `sample_*.csv`：数据样本文件（调试用）
- 日志文件：记录运行状态

### 数据格式
- 所有价格数据：元
- 涨跌幅：百分比
- 成交量：股
- 成交额：元

## 🔄 更新和维护

### 定期更新
```bash
# 更新依赖包
pip install --upgrade akshare pandas numpy

# 检查系统状态
python test_system.py

# 清理缓存文件
del *.png *.csv
```

### 性能优化
- 减少同时分析的股票数量
- 调整数据获取频率
- 使用更快的网络连接

## 📞 技术支持

### 日志查看
- 运行时日志会显示在控制台
- 错误信息包含具体的失败原因
- 可以根据日志信息进行问题排查

### 问题反馈
- 记录错误信息和操作步骤
- 提供系统测试结果
- 说明使用环境（Python版本、操作系统等）

---

**免责声明**：本系统仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。
