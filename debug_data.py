"""
调试数据结构 - 检查AKShare返回的实际数据格式
"""

import akshare as ak
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_industry_data_structure():
    """检查行业数据的实际结构"""
    print("🔍 检查AKShare行业数据结构...")
    
    # 测试不同的API
    apis_to_test = [
        ('index_stock_info_sw', '申万行业指数'),
        ('stock_board_industry_name_em', '东财行业板块'),
        ('stock_board_concept_name_em', '东财概念板块'),
        ('stock_board_concept_name_ths', '同花顺概念板块')
    ]
    
    for api_name, description in apis_to_test:
        try:
            print(f"\n📊 测试 {description} ({api_name}):")
            
            # 动态调用API
            api_func = getattr(ak, api_name)
            data = api_func()
            
            if not data.empty:
                print(f"   ✅ 成功获取数据，共 {len(data)} 行")
                print(f"   📋 列名: {list(data.columns)}")
                print(f"   📄 前3行数据:")
                print(data.head(3).to_string(index=False))
                
                # 保存样本数据
                data.head(10).to_csv(f'sample_{api_name}.csv', index=False, encoding='utf-8-sig')
                print(f"   💾 样本数据已保存到 sample_{api_name}.csv")
            else:
                print(f"   ❌ 未获取到数据")
                
        except Exception as e:
            print(f"   ❌ API调用失败: {e}")

def check_stock_data_structure():
    """检查股票数据结构"""
    print("\n🔍 检查股票数据结构...")
    
    try:
        # 测试股票基本信息
        stock_info = ak.stock_info_a_code_name()
        print(f"📊 股票基本信息:")
        print(f"   ✅ 共 {len(stock_info)} 只股票")
        print(f"   📋 列名: {list(stock_info.columns)}")
        print(f"   📄 前3行:")
        print(stock_info.head(3).to_string(index=False))
        
        # 测试单只股票历史数据
        print(f"\n📊 股票历史数据 (000001):")
        stock_hist = ak.stock_zh_a_hist(symbol="000001", period="daily", 
                                       start_date="20240101", end_date="20240131")
        if not stock_hist.empty:
            print(f"   ✅ 共 {len(stock_hist)} 条记录")
            print(f"   📋 列名: {list(stock_hist.columns)}")
            print(f"   📄 前3行:")
            print(stock_hist.head(3).to_string(index=False))
        
    except Exception as e:
        print(f"❌ 检查股票数据失败: {e}")

if __name__ == "__main__":
    check_industry_data_structure()
    check_stock_data_structure()
