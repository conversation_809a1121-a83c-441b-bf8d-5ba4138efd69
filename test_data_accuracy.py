#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据准确性测试脚本
验证封板预测系统的数据质量保障机制
"""

import pandas as pd
import numpy as np
from limit_up_page import validate_stock_data_integrity, calculate_overall_data_quality

def create_test_data():
    """创建测试数据集"""
    test_cases = []
    
    # 1. 完美数据
    perfect_data = {
        '代码': '000001',
        '名称': '平安银行',
        '最新价': 12.50,
        '昨收': 12.00,
        '涨跌幅': 4.17,
        '成交量': 50000000,
        '成交额': 625000000,
        '换手率': 2.5
    }
    test_cases.append(('完美数据', perfect_data))
    
    # 2. 缺少必需字段
    missing_field_data = {
        '代码': '000002',
        '名称': '万科A',
        '最新价': 15.20,
        '昨收': 14.80,
        # 缺少涨跌幅
        '成交量': 30000000,
        '成交额': 456000000,
        '换手率': 1.8
    }
    test_cases.append(('缺少字段', missing_field_data))
    
    # 3. 价格异常数据
    price_error_data = {
        '代码': '600519',
        '名称': '贵州茅台',
        '最新价': -1800.00,  # 负价格
        '昨收': 1750.00,
        '涨跌幅': -102.86,
        '成交量': 1000000,
        '成交额': -1800000000,  # 负成交额
        '换手率': 0.5
    }
    test_cases.append(('价格异常', price_error_data))
    
    # 4. 涨跌幅不一致
    inconsistent_change_data = {
        '代码': '000858',
        '名称': '五粮液',
        '最新价': 150.00,
        '昨收': 140.00,
        '涨跌幅': 5.00,  # 实际应该是7.14%
        '成交量': 20000000,
        '成交额': 3000000000,
        '换手率': 3.2
    }
    test_cases.append(('涨跌幅不一致', inconsistent_change_data))
    
    # 5. 换手率异常
    turnover_error_data = {
        '代码': '002415',
        '名称': '海康威视',
        '最新价': 35.50,
        '昨收': 34.00,
        '涨跌幅': 4.41,
        '成交量': 80000000,
        '成交额': 2840000000,
        '换手率': 150.0  # 换手率>100%
    }
    test_cases.append(('换手率异常', turnover_error_data))
    
    # 6. 数据类型错误
    type_error_data = {
        '代码': '300059',
        '名称': '东方财富',
        '最新价': 'invalid',  # 字符串而非数字
        '昨收': 25.00,
        '涨跌幅': 'error',
        '成交量': 100000000,
        '成交额': 2500000000,
        '换手率': 4.5
    }
    test_cases.append(('数据类型错误', type_error_data))
    
    # 7. 边界值测试
    boundary_data = {
        '代码': '688001',
        '名称': '华兴源创',
        '最新价': 999.99,  # 高价格
        '昨收': 950.00,
        '涨跌幅': 5.26,
        '成交量': 1,  # 极小成交量
        '成交额': 999.99,
        '换手率': 0.01  # 极小换手率
    }
    test_cases.append(('边界值测试', boundary_data))
    
    # 8. 成交数据不匹配
    volume_mismatch_data = {
        '代码': '002594',
        '名称': '比亚迪',
        '最新价': 250.00,
        '昨收': 240.00,
        '涨跌幅': 4.17,
        '成交量': 1000000,  # 100万股
        '成交额': 5000000000,  # 50亿元，均价5000元/股，明显不匹配
        '换手率': 2.0
    }
    test_cases.append(('成交数据不匹配', volume_mismatch_data))
    
    return test_cases

def run_data_accuracy_test():
    """运行数据准确性测试"""
    print("🔍 数据准确性保障机制测试")
    print("=" * 60)
    print()
    
    test_cases = create_test_data()
    results = []
    
    for test_name, test_data in test_cases:
        print(f"📊 测试案例: {test_name}")
        print("-" * 40)
        
        # 执行数据验证
        validation_result = validate_stock_data_integrity(test_data)
        
        # 显示验证结果
        print(f"✅ 验证通过: {'是' if validation_result['is_valid'] else '否'}")
        print(f"📈 质量评分: {validation_result['data_quality_score']:.0f}分")
        
        if validation_result['errors']:
            print("❌ 错误信息:")
            for error in validation_result['errors']:
                print(f"   • {error}")
        
        if validation_result['warnings']:
            print("⚠️ 警告信息:")
            for warning in validation_result['warnings']:
                print(f"   • {warning}")
        
        # 记录结果
        results.append({
            'test_name': test_name,
            'is_valid': validation_result['is_valid'],
            'quality_score': validation_result['data_quality_score'],
            'error_count': len(validation_result['errors']),
            'warning_count': len(validation_result['warnings'])
        })
        
        print()
    
    # 测试总结
    print("📋 测试总结")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = len([r for r in results if r['is_valid']])
    failed_tests = total_tests - passed_tests
    
    print(f"总测试案例: {total_tests}")
    print(f"验证通过: {passed_tests}")
    print(f"验证失败: {failed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    print()
    
    # 质量分布
    high_quality = len([r for r in results if r['quality_score'] >= 90])
    medium_quality = len([r for r in results if 70 <= r['quality_score'] < 90])
    low_quality = len([r for r in results if r['quality_score'] < 70])
    
    print("📊 质量分布:")
    print(f"高质量 (≥90分): {high_quality} 个")
    print(f"中等质量 (70-89分): {medium_quality} 个")
    print(f"低质量 (<70分): {low_quality} 个")
    print()
    
    # 错误统计
    total_errors = sum(r['error_count'] for r in results)
    total_warnings = sum(r['warning_count'] for r in results)
    
    print("⚠️ 问题统计:")
    print(f"总错误数: {total_errors}")
    print(f"总警告数: {total_warnings}")
    print()
    
    # 测试整体数据质量评估
    print("🎯 整体质量评估测试")
    print("-" * 30)
    
    quality_stats = {
        'total_processed': total_tests,
        'validation_passed': passed_tests,
        'validation_failed': failed_tests,
        'high_quality_data': high_quality,
        'medium_quality_data': medium_quality,
        'low_quality_data': low_quality,
        'data_warnings': [f"测试警告{i}" for i in range(total_warnings)]
    }
    
    overall_quality = calculate_overall_data_quality(quality_stats)
    print(f"整体数据质量评分: {overall_quality:.1f}分")
    
    if overall_quality >= 95:
        print("🟢 评级: 优秀 - 数据质量保障机制运行良好")
    elif overall_quality >= 85:
        print("🟡 评级: 良好 - 数据质量保障基本有效")
    elif overall_quality >= 70:
        print("🟠 评级: 一般 - 数据质量保障需要改进")
    else:
        print("🔴 评级: 较差 - 数据质量保障存在问题")
    
    print()
    print("✅ 数据准确性保障机制测试完成!")
    print()
    print("🛡️ 保障机制特性:")
    print("  • 必需字段完整性检查")
    print("  • 数据类型和范围验证")
    print("  • 价格合理性检查")
    print("  • 涨跌幅一致性验证")
    print("  • 成交数据合理性检查")
    print("  • 股票代码格式验证")
    print("  • 数据时效性检查")
    print("  • 质量评分和等级划分")
    print("  • 详细错误和警告信息")
    print("  • 整体质量统计报告")

if __name__ == "__main__":
    run_data_accuracy_test()
