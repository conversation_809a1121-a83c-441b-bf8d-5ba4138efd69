"""
数据获取模块 - 股票数据分析系统
"""

import pandas as pd
import numpy as np
import akshare as ak
import yfinance as yf
import requests
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List, Optional, Tuple
from config import *

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataFetcher:
    """数据获取类"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def get_stock_basic_info(self) -> pd.DataFrame:
        """获取股票基本信息"""
        try:
            # 获取A股股票基本信息
            stock_info = ak.stock_info_a_code_name()
            logger.info(f"获取到 {len(stock_info)} 只股票基本信息")
            return stock_info
        except Exception as e:
            logger.error(f"获取股票基本信息失败: {e}")
            return pd.DataFrame()

    def get_stock_price_data(self, symbol: str, period: str = "1y") -> pd.DataFrame:
        """获取股票价格数据"""
        try:
            # 使用akshare获取A股数据
            if symbol.startswith(('6', '0', '3')):
                # A股代码
                stock_data = ak.stock_zh_a_hist(symbol=symbol, period="daily",
                                              start_date=(datetime.now() - timedelta(days=365)).strftime('%Y%m%d'),
                                              end_date=datetime.now().strftime('%Y%m%d'))
                if not stock_data.empty:
                    stock_data['日期'] = pd.to_datetime(stock_data['日期'])
                    stock_data.set_index('日期', inplace=True)
                    # 重命名列以符合标准格式
                    stock_data.rename(columns={
                        '开盘': 'Open',
                        '收盘': 'Close',
                        '最高': 'High',
                        '最低': 'Low',
                        '成交量': 'Volume'
                    }, inplace=True)
                return stock_data
            else:
                logger.warning(f"不支持的股票代码格式: {symbol}")
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取股票 {symbol} 价格数据失败: {e}")
            return pd.DataFrame()

    def get_industry_data(self) -> Dict[str, pd.DataFrame]:
        """获取行业数据"""
        industry_data = {}
        try:
            # 优先获取东财行业板块数据（数据质量较好）
            try:
                sw_industry = ak.stock_board_industry_name_em()
                if not sw_industry.empty:
                    industry_data['sw_industry'] = sw_industry
                    logger.info(f"成功获取东财行业板块数据: {len(sw_industry)} 个行业")
            except Exception as e:
                logger.warning(f"获取东财行业板块数据失败: {e}")

            # 如果没有获取到行业数据，尝试概念板块数据
            if 'sw_industry' not in industry_data:
                try:
                    concept_data = ak.stock_board_concept_name_em()
                    if not concept_data.empty:
                        industry_data['sw_industry'] = concept_data  # 使用概念数据作为行业数据
                        logger.info(f"使用东财概念板块数据作为行业数据: {len(concept_data)} 个概念")
                except Exception as e:
                    logger.warning(f"获取东财概念板块数据失败: {e}")

            # 获取额外的概念板块数据
            try:
                concept_data = ak.stock_board_concept_name_em()
                if not concept_data.empty:
                    industry_data['concept'] = concept_data
                    logger.info(f"成功获取概念板块数据: {len(concept_data)} 个概念")
            except Exception as e:
                logger.warning(f"获取概念板块数据失败: {e}")

            # 如果还是没有数据，尝试同花顺概念数据
            if not industry_data:
                try:
                    ths_concept = ak.stock_board_concept_name_ths()
                    if not ths_concept.empty:
                        industry_data['sw_industry'] = ths_concept
                        logger.info(f"使用同花顺概念数据: {len(ths_concept)} 个概念")
                except Exception as e:
                    logger.warning(f"获取同花顺概念数据失败: {e}")

            if industry_data:
                logger.info("成功获取行业数据")
            else:
                logger.warning("未获取到任何行业数据")
            return industry_data
        except Exception as e:
            logger.error(f"获取行业数据失败: {e}")
            return {}

    def get_market_sentiment(self) -> Dict:
        """获取市场情绪指标"""
        try:
            sentiment_data = {}

            # 获取北向资金数据
            north_money = ak.stock_hsgt_north_net_flow_in_em()
            sentiment_data['north_money'] = north_money

            # 获取融资融券数据
            margin_data = ak.stock_margin_sse()
            sentiment_data['margin'] = margin_data

            # 获取新股申购数据
            new_stock = ak.stock_ipo_summary_cninfo()
            sentiment_data['new_stock'] = new_stock

            logger.info("成功获取市场情绪数据")
            return sentiment_data
        except Exception as e:
            logger.error(f"获取市场情绪数据失败: {e}")
            return {}

    def get_financial_data(self, symbol: str) -> pd.DataFrame:
        """获取财务数据"""
        try:
            # 获取财务指标
            financial_data = ak.stock_financial_abstract_ths(symbol=symbol)
            logger.info(f"获取股票 {symbol} 财务数据成功")
            return financial_data
        except Exception as e:
            logger.error(f"获取股票 {symbol} 财务数据失败: {e}")
            return pd.DataFrame()

    def get_policy_news(self, keywords: List[str], days: int = 7) -> List[Dict]:
        """获取政策相关新闻"""
        try:
            news_list = []

            # 获取财经新闻
            for keyword in keywords:
                try:
                    # 这里可以接入新闻API或爬虫
                    # 示例：使用akshare获取新闻
                    news_data = ak.stock_news_em()
                    if not news_data.empty:
                        # 过滤包含关键词的新闻
                        filtered_news = news_data[
                            news_data['新闻标题'].str.contains('|'.join(keywords), na=False)
                        ]
                        for _, row in filtered_news.head(10).iterrows():
                            news_list.append({
                                'title': row['新闻标题'],
                                'content': row.get('新闻内容', ''),
                                'time': row.get('发布时间', ''),
                                'source': row.get('新闻来源', ''),
                                'keyword': keyword
                            })
                    time.sleep(0.5)  # 避免请求过快
                except Exception as e:
                    logger.warning(f"获取关键词 {keyword} 相关新闻失败: {e}")
                    continue

            logger.info(f"获取到 {len(news_list)} 条政策相关新闻")
            return news_list
        except Exception as e:
            logger.error(f"获取政策新闻失败: {e}")
            return []

    def get_leading_stocks_data(self, industry: str) -> pd.DataFrame:
        """获取行业龙头股票数据"""
        try:
            if industry not in LEADING_STOCKS:
                logger.warning(f"未找到行业 {industry} 的龙头股票配置")
                return pd.DataFrame()

            stocks_data = []
            for stock_name, stock_code in LEADING_STOCKS[industry].items():
                try:
                    # 获取股票基本信息
                    stock_info = ak.stock_individual_info_em(symbol=stock_code)
                    if not stock_info.empty:
                        stock_data = {
                            'name': stock_name,
                            'code': stock_code,
                            'industry': industry
                        }
                        # 添加基本信息
                        for _, row in stock_info.iterrows():
                            stock_data[row['item']] = row['value']
                        stocks_data.append(stock_data)
                    time.sleep(0.2)  # 避免请求过快
                except Exception as e:
                    logger.warning(f"获取股票 {stock_name}({stock_code}) 数据失败: {e}")
                    continue

            result_df = pd.DataFrame(stocks_data)
            logger.info(f"获取到 {len(result_df)} 只 {industry} 行业龙头股票数据")
            return result_df
        except Exception as e:
            logger.error(f"获取行业 {industry} 龙头股票数据失败: {e}")
            return pd.DataFrame()

    def get_index_data(self, index_code: str = "000001") -> pd.DataFrame:
        """获取指数数据"""
        try:
            # 获取指数历史数据
            index_data = ak.stock_zh_index_daily(symbol=f"sh{index_code}")
            if not index_data.empty:
                index_data['date'] = pd.to_datetime(index_data['date'])
                index_data.set_index('date', inplace=True)
            logger.info(f"获取指数 {index_code} 数据成功")
            return index_data
        except Exception as e:
            logger.error(f"获取指数 {index_code} 数据失败: {e}")
            return pd.DataFrame()
