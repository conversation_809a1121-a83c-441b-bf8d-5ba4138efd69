#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多线程性能测试脚本
测试封板预测系统的多线程加速效果
"""

import time
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import threading

def simulate_stock_analysis(stock_data, delay=0.5):
    """模拟股票分析过程"""
    # 模拟分析耗时
    time.sleep(delay)
    
    # 模拟分析结果
    result = {
        'stock_code': stock_data.get('code', '000000'),
        'stock_name': stock_data.get('name', '测试股票'),
        'probability': np.random.uniform(30, 90),
        'analysis_time': delay
    }
    return result

def single_thread_analysis(stocks_data, delay=0.5):
    """单线程分析"""
    print("🔄 开始单线程分析...")
    start_time = time.time()
    
    results = []
    for i, stock in enumerate(stocks_data):
        print(f"  分析进度: {i+1}/{len(stocks_data)} - {stock['name']}")
        result = simulate_stock_analysis(stock, delay)
        results.append(result)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"✅ 单线程分析完成")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   分析速度: {len(stocks_data)/total_time:.2f}只/秒")
    
    return results, total_time

def multi_thread_analysis(stocks_data, max_workers=4, delay=0.5):
    """多线程分析"""
    print(f"⚡ 开始{max_workers}线程分析...")
    start_time = time.time()
    
    results = []
    completed_count = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_stock = {}
        for stock in stocks_data:
            future = executor.submit(simulate_stock_analysis, stock, delay)
            future_to_stock[future] = stock
        
        # 收集结果
        for future in future_to_stock:
            stock = future_to_stock[future]
            completed_count += 1
            print(f"  分析进度: {completed_count}/{len(stocks_data)} - {stock['name']}")
            
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"  ❌ 分析失败: {stock['name']} - {e}")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"✅ {max_workers}线程分析完成")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   分析速度: {len(stocks_data)/total_time:.2f}只/秒")
    
    return results, total_time

def performance_test():
    """性能测试主函数"""
    print("🚀 封板预测多线程性能测试")
    print("=" * 50)
    
    # 生成测试数据
    test_stocks = []
    for i in range(20):  # 测试20只股票
        stock = {
            'code': f'00000{i:02d}',
            'name': f'测试股票{i+1:02d}',
            'price': np.random.uniform(10, 100)
        }
        test_stocks.append(stock)
    
    print(f"📊 测试数据: {len(test_stocks)} 只股票")
    print(f"🕐 模拟分析时间: 0.5秒/只")
    print()
    
    # 单线程测试
    single_results, single_time = single_thread_analysis(test_stocks, delay=0.5)
    print()
    
    # 多线程测试
    thread_configs = [2, 4, 6, 8]
    multi_results = {}
    
    for workers in thread_configs:
        results, multi_time = multi_thread_analysis(test_stocks, workers, delay=0.5)
        multi_results[workers] = multi_time
        
        # 计算加速比
        speedup = single_time / multi_time
        efficiency = speedup / workers * 100
        
        print(f"   🚀 加速比: {speedup:.2f}x")
        print(f"   📈 效率: {efficiency:.1f}%")
        print()
    
    # 性能总结
    print("📊 性能测试总结:")
    print("-" * 30)
    print(f"单线程基准: {single_time:.2f}秒")
    
    for workers, multi_time in multi_results.items():
        speedup = single_time / multi_time
        time_saved = single_time - multi_time
        print(f"{workers}线程: {multi_time:.2f}秒 (提速{speedup:.1f}x, 节省{time_saved:.1f}秒)")
    
    print()
    print("💡 结论:")
    best_workers = min(multi_results.keys(), key=lambda x: multi_results[x])
    best_speedup = single_time / multi_results[best_workers]
    print(f"   最佳配置: {best_workers}线程")
    print(f"   最大提速: {best_speedup:.1f}倍")
    print(f"   推荐使用: 4线程 (性能与资源平衡)")

if __name__ == "__main__":
    performance_test()
