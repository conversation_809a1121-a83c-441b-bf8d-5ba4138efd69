"""
投资决策支持模块
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List

logger = logging.getLogger(__name__)

class InvestmentAdvisor:
    """投资决策顾问"""
    
    def __init__(self):
        self.risk_profiles = {
            'conservative': {'max_single_position': 0.03, 'max_small_cap': 0.15, 'stop_loss': 0.08},
            'moderate': {'max_single_position': 0.05, 'max_small_cap': 0.30, 'stop_loss': 0.10},
            'aggressive': {'max_single_position': 0.08, 'max_small_cap': 0.50, 'stop_loss': 0.15}
        }
    
    def generate_investment_decision(self, stock_analysis: dict, technical_analysis: dict, 
                                   fund_flow_analysis: dict, user_profile: dict) -> dict:
        """生成投资决策"""
        try:
            decision = {
                'stock_code': stock_analysis.get('stock_code', ''),
                'decision_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'action': 'hold',  # buy, sell, hold
                'confidence': 0,
                'position_size': 0,
                'entry_price': 0,
                'target_price': 0,
                'stop_loss': 0,
                'holding_period': '中期',
                'risk_assessment': {},
                'decision_factors': [],
                'warnings': []
            }
            
            # 获取用户风险偏好
            risk_profile = user_profile.get('risk_tolerance', 'moderate')
            portfolio_size = user_profile.get('portfolio_size', 100000)
            
            # 综合评分计算
            total_score = 0
            max_score = 100
            
            # 1. 基本面评分 (40%)
            fundamental_score = stock_analysis.get('total_score', 0)
            total_score += fundamental_score * 0.4
            decision['decision_factors'].append(f"基本面评分: {fundamental_score:.1f}/100")
            
            # 2. 技术面评分 (30%)
            technical_score = self._calculate_technical_score(technical_analysis)
            total_score += technical_score * 0.3
            decision['decision_factors'].append(f"技术面评分: {technical_score:.1f}/100")
            
            # 3. 资金流向评分 (20%)
            fund_score = self._calculate_fund_flow_score(fund_flow_analysis)
            total_score += fund_score * 0.2
            decision['decision_factors'].append(f"资金流向评分: {fund_score:.1f}/100")
            
            # 4. 市场环境评分 (10%)
            market_score = 60  # 默认中性
            total_score += market_score * 0.1
            
            decision['confidence'] = min(100, total_score)
            
            # 决策逻辑
            current_price = stock_analysis.get('valuation', {}).get('current_price', 0)
            
            if total_score >= 75:
                decision['action'] = 'buy'
                decision['entry_price'] = current_price
                decision['target_price'] = current_price * 1.2  # 20%目标收益
                decision['holding_period'] = '中长期'
            elif total_score >= 60:
                decision['action'] = 'buy'
                decision['entry_price'] = current_price
                decision['target_price'] = current_price * 1.15  # 15%目标收益
                decision['holding_period'] = '中期'
            elif total_score >= 45:
                decision['action'] = 'hold'
                decision['holding_period'] = '观察'
            else:
                decision['action'] = 'avoid'
                decision['warnings'].append("综合评分较低，建议避免投资")
            
            # 仓位管理
            if decision['action'] == 'buy':
                decision.update(self._calculate_position_size(
                    stock_analysis, risk_profile, portfolio_size
                ))
            
            # 风险评估
            decision['risk_assessment'] = self._assess_investment_risk(
                stock_analysis, technical_analysis, fund_flow_analysis
            )
            
            # 止损设置
            risk_config = self.risk_profiles[risk_profile]
            decision['stop_loss'] = current_price * (1 - risk_config['stop_loss'])
            
            # 生成具体建议
            decision['detailed_advice'] = self._generate_detailed_advice(decision, stock_analysis)
            
            logger.info(f"生成股票 {decision['stock_code']} 投资决策: {decision['action']}")
            return decision
            
        except Exception as e:
            logger.error(f"生成投资决策失败: {e}")
            return {}
    
    def _calculate_technical_score(self, technical_analysis: dict) -> float:
        """计算技术面评分"""
        if not technical_analysis:
            return 50
        
        score = 0
        signals = technical_analysis.get('signals', {})
        
        # 综合信号评分
        overall_signal = signals.get('overall_signal', 'neutral')
        if overall_signal == 'buy':
            score += 40
        elif overall_signal == 'bullish':
            score += 30
        elif overall_signal == 'hold':
            score += 20
        elif overall_signal == 'neutral':
            score += 15
        else:
            score += 5
        
        # RSI评分
        indicators = technical_analysis.get('indicators', {})
        rsi = indicators.get('RSI', 50)
        if 30 <= rsi <= 70:
            score += 20
        elif rsi < 30:
            score += 25  # 超卖机会
        elif rsi > 70:
            score += 10  # 超买风险
        
        # MACD评分
        macd_signal = signals.get('MACD', 'neutral')
        if macd_signal == 'bullish':
            score += 20
        elif macd_signal == 'neutral':
            score += 10
        
        # 移动平均线评分
        ma_signal = signals.get('MA', 'neutral')
        if ma_signal == 'bullish':
            score += 20
        elif ma_signal == 'neutral':
            score += 10
        
        return min(100, score)
    
    def _calculate_fund_flow_score(self, fund_flow_analysis: dict) -> float:
        """计算资金流向评分"""
        if not fund_flow_analysis:
            return 50
        
        score = 0
        
        # 主力资金趋势
        main_trend = fund_flow_analysis.get('main_fund_trend', 'neutral')
        if main_trend == 'strong_inflow':
            score += 40
        elif main_trend == 'inflow':
            score += 30
        elif main_trend == 'neutral':
            score += 20
        else:
            score += 10
        
        # 大单活跃度
        big_deal_activity = fund_flow_analysis.get('big_deal_activity', 'normal')
        if big_deal_activity == 'very_active':
            score += 30
        elif big_deal_activity == 'active':
            score += 20
        else:
            score += 10
        
        # 资金流向评分
        fund_score = fund_flow_analysis.get('fund_flow_score', 0)
        score += fund_score * 0.3
        
        return min(100, score)
    
    def _calculate_position_size(self, stock_analysis: dict, risk_profile: str, portfolio_size: float) -> dict:
        """计算仓位大小"""
        risk_config = self.risk_profiles[risk_profile]
        
        # 基础仓位
        base_position = risk_config['max_single_position']
        
        # 根据股票质量调整
        total_score = stock_analysis.get('total_score', 0)
        if total_score >= 80:
            position_multiplier = 1.0
        elif total_score >= 65:
            position_multiplier = 0.8
        else:
            position_multiplier = 0.6
        
        # 小市值股票限制
        market_cap_rating = stock_analysis.get('valuation', {}).get('market_cap_rating', '')
        if market_cap_rating in ['小市值', '中小市值']:
            max_small_cap_position = risk_config['max_small_cap'] * 0.2  # 单只小市值股票限制
            base_position = min(base_position, max_small_cap_position)
        
        final_position = base_position * position_multiplier
        position_value = portfolio_size * final_position
        
        current_price = stock_analysis.get('valuation', {}).get('current_price', 0)
        shares = int(position_value / current_price / 100) * 100 if current_price > 0 else 0  # 整手
        
        return {
            'position_size': final_position,
            'position_value': position_value,
            'recommended_shares': shares,
            'position_percentage': f"{final_position*100:.1f}%"
        }
    
    def _assess_investment_risk(self, stock_analysis: dict, technical_analysis: dict, 
                               fund_flow_analysis: dict) -> dict:
        """评估投资风险"""
        risk_factors = []
        risk_score = 0  # 0-100，越高风险越大
        
        # 估值风险
        valuation = stock_analysis.get('valuation', {})
        pe_rating = valuation.get('pe_rating', '')
        if pe_rating in ['高估', '偏高']:
            risk_factors.append("估值偏高风险")
            risk_score += 20
        
        # 市值风险
        market_cap_rating = valuation.get('market_cap_rating', '')
        if market_cap_rating == '小市值':
            risk_factors.append("小市值波动风险")
            risk_score += 25
        elif market_cap_rating == '中小市值':
            risk_factors.append("中小市值波动风险")
            risk_score += 15
        
        # 技术面风险
        if technical_analysis:
            indicators = technical_analysis.get('indicators', {})
            rsi = indicators.get('RSI', 50)
            if rsi > 80:
                risk_factors.append("技术面超买风险")
                risk_score += 15
        
        # 资金流向风险
        if fund_flow_analysis:
            main_trend = fund_flow_analysis.get('main_fund_trend', 'neutral')
            if main_trend == 'outflow':
                risk_factors.append("资金流出风险")
                risk_score += 20
        
        # 成长性风险
        growth = stock_analysis.get('growth_potential', {})
        if growth.get('growth_rating') == '较差':
            risk_factors.append("成长性不足风险")
            risk_score += 15
        
        # 风险等级
        if risk_score <= 20:
            risk_level = '低风险'
        elif risk_score <= 40:
            risk_level = '中低风险'
        elif risk_score <= 60:
            risk_level = '中等风险'
        elif risk_score <= 80:
            risk_level = '中高风险'
        else:
            risk_level = '高风险'
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors
        }
    
    def _generate_detailed_advice(self, decision: dict, stock_analysis: dict) -> list:
        """生成详细投资建议"""
        advice = []
        
        action = decision['action']
        confidence = decision['confidence']
        
        if action == 'buy':
            advice.append(f"🎯 **投资建议**: 建议买入 (置信度: {confidence:.1f}%)")
            advice.append(f"💰 **建议仓位**: {decision['position_percentage']}")
            advice.append(f"📈 **目标价位**: {decision['target_price']:.2f}元")
            advice.append(f"🛡️ **止损价位**: {decision['stop_loss']:.2f}元")
            advice.append(f"⏰ **持有周期**: {decision['holding_period']}")
            
            # 买入时机建议
            if confidence >= 80:
                advice.append("✅ **买入时机**: 综合评分优秀，可考虑立即买入")
            elif confidence >= 65:
                advice.append("⚡ **买入时机**: 评分良好，可分批买入")
            else:
                advice.append("⏳ **买入时机**: 建议等待更好的买入机会")
        
        elif action == 'hold':
            advice.append("⏸️ **投资建议**: 暂时观察，等待更明确信号")
            advice.append("👀 **关注要点**: 密切关注基本面和技术面变化")
        
        else:
            advice.append("❌ **投资建议**: 不建议投资")
            advice.append("⚠️ **风险提示**: 综合评分较低，存在较大投资风险")
        
        # 风险提示
        risk_assessment = decision.get('risk_assessment', {})
        risk_factors = risk_assessment.get('risk_factors', [])
        if risk_factors:
            advice.append("🚨 **风险提示**:")
            for factor in risk_factors:
                advice.append(f"   • {factor}")
        
        # 操作建议
        advice.append("📋 **操作建议**:")
        advice.append("   • 严格执行止损策略")
        advice.append("   • 控制单只股票仓位")
        advice.append("   • 定期复盘调整策略")
        
        return advice
    
    def generate_portfolio_advice(self, portfolio_analysis: list) -> dict:
        """生成投资组合建议"""
        try:
            portfolio_advice = {
                'total_stocks': len(portfolio_analysis),
                'recommended_actions': {'buy': 0, 'hold': 0, 'sell': 0},
                'risk_distribution': {},
                'sector_allocation': {},
                'overall_recommendation': [],
                'rebalancing_advice': []
            }
            
            # 统计各种建议
            for stock in portfolio_analysis:
                action = stock.get('action', 'hold')
                portfolio_advice['recommended_actions'][action] += 1
            
            # 生成组合建议
            buy_count = portfolio_advice['recommended_actions']['buy']
            total_count = portfolio_advice['total_stocks']
            
            if buy_count / total_count > 0.6:
                portfolio_advice['overall_recommendation'].append("🚀 当前市场机会较多，可适当增加仓位")
            elif buy_count / total_count > 0.3:
                portfolio_advice['overall_recommendation'].append("⚖️ 市场机会适中，保持均衡配置")
            else:
                portfolio_advice['overall_recommendation'].append("🛡️ 市场机会较少，建议谨慎操作")
            
            return portfolio_advice
            
        except Exception as e:
            logger.error(f"生成投资组合建议失败: {e}")
            return {}
