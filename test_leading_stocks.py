"""
龙头股票推荐功能测试
"""

from industry_analyzer import IndustryAnalyzer
from technical_analyzer import TechnicalAnalyzer
from config import LEADING_STOCKS

def test_leading_stocks_recommendation():
    """测试龙头股票推荐功能"""
    print('🚀 龙头股票推荐系统完整测试')
    print('='*60)

    # 初始化
    industry_analyzer = IndustryAnalyzer()
    technical_analyzer = TechnicalAnalyzer()

    # 获取强势行业
    performance_df = industry_analyzer.calculate_industry_performance()
    signals = industry_analyzer.detect_rotation_signals(performance_df)
    strong_industries = signals.get('strong_industries', [])

    print(f'📈 当前强势行业: {len(strong_industries)}个')
    for i, industry in enumerate(strong_industries[:5], 1):
        industry_data = performance_df[performance_df['industry'] == industry]
        if not industry_data.empty:
            change_pct = industry_data.iloc[0]['change_pct_1d']
            print(f'  {i}. {industry} ({change_pct:+.2f}%)')

    print('\n🎯 龙头股票推荐分析:')
    print('='*60)

    recommended_stocks = []

    for industry in strong_industries[:3]:
        mapped_industry = industry_analyzer._map_industry_name(industry)
        
        if mapped_industry and mapped_industry in LEADING_STOCKS:
            print(f'\n📊 {industry} 行业 -> {mapped_industry}')
            print('-' * 40)
            
            industry_stocks = []
            for stock_name, stock_code in list(LEADING_STOCKS[mapped_industry].items())[:3]:
                try:
                    tech_analysis = technical_analyzer.analyze_stock_technical(stock_code)
                    
                    if tech_analysis:
                        price = tech_analysis.get('current_price', 0)
                        signal = tech_analysis.get('signals', {}).get('overall_signal', 'hold')
                        rsi = tech_analysis.get('indicators', {}).get('RSI', 0)
                        
                        # 计算推荐评分
                        score = 0
                        if signal == 'buy':
                            score += 3
                        elif signal == 'bullish':
                            score += 2
                        elif signal == 'hold':
                            score += 1
                        
                        if 30 <= rsi <= 70:
                            score += 2
                        elif rsi < 30:
                            score += 3
                        
                        signal_emoji = '🟢' if signal in ['buy', 'bullish'] else '🟡' if signal == 'hold' else '🔴'
                        
                        stock_info = {
                            'name': stock_name,
                            'code': stock_code,
                            'price': price,
                            'signal': signal,
                            'rsi': rsi,
                            'score': score,
                            'industry': industry
                        }
                        
                        industry_stocks.append(stock_info)
                        recommended_stocks.append(stock_info)
                        
                        print(f'{signal_emoji} {stock_name}({stock_code})')
                        print(f'   价格: {price:.2f}元')
                        print(f'   信号: {signal}')
                        print(f'   RSI: {rsi:.1f}')
                        print(f'   评分: {score}/5')
                        print()
                        
                except Exception as e:
                    print(f'❌ {stock_name}({stock_code}): 分析失败')

    print('\n🏆 综合推荐排行榜:')
    print('='*60)

    # 按评分排序
    recommended_stocks.sort(key=lambda x: x['score'], reverse=True)

    for i, stock in enumerate(recommended_stocks[:8], 1):
        signal_emoji = '🟢' if stock['signal'] in ['buy', 'bullish'] else '🟡'
        print(f'{i:2d}. {signal_emoji} {stock["name"]} ({stock["code"]})')
        print(f'     行业: {stock["industry"]} | 价格: {stock["price"]:.2f}元 | 信号: {stock["signal"]} | 评分: {stock["score"]}/5')

    # 投资建议
    buy_signals = [s for s in recommended_stocks if s['signal'] in ['buy', 'bullish']]
    high_score = [s for s in recommended_stocks if s['score'] >= 4]

    print('\n💡 投资建议:')
    print('='*60)

    if buy_signals:
        print(f'🎯 买入信号: {len(buy_signals)}只股票发出积极信号')
        for stock in buy_signals[:3]:
            print(f'   • {stock["name"]} - {stock["signal"]} (评分: {stock["score"]}/5)')

    if high_score:
        print(f'\n⭐ 高评分股票: {len(high_score)}只股票评分≥4分')
        for stock in high_score[:3]:
            print(f'   • {stock["name"]} - 评分: {stock["score"]}/5')

    print('\n🎉 龙头股票推荐功能测试完成！')
    print('✅ 功能包括: 强势行业识别 + 龙头股票筛选 + 技术分析评分 + 综合排行')
    
    return recommended_stocks

if __name__ == "__main__":
    test_leading_stocks_recommendation()
