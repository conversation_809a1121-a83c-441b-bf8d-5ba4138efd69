"""
回测功能测试脚本
"""

from backtest_engine import BacktestEngine, ma_crossover_strategy
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_backtest():
    """测试回测功能"""
    print("🔬 测试回测功能...")

    try:
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000)
        engine.add_strategy(ma_crossover_strategy, "移动平均线交叉策略")

        # 设置回测参数
        symbols = ['000001']  # 平安银行
        end_date = datetime(2024, 12, 31)  # 使用历史日期
        start_date = datetime(2024, 10, 1)  # 3个月的历史数据

        print(f"回测股票: {symbols}")
        print(f"回测期间: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        print(f"初始资金: {engine.initial_capital:,.0f}元")

        # 运行回测
        results = engine.run_backtest(
            symbols=symbols,
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d'),
            strategy_params={'short_period': 5, 'long_period': 20}
        )

        if results:
            print("\n✅ 回测完成！")
            print("=" * 50)
            print("📊 回测结果:")
            print(f"策略名称: {results['strategy_name']}")
            print(f"回测期间: {results['start_date']} 到 {results['end_date']}")
            print(f"初始资金: {results['initial_capital']:,.0f}元")
            print(f"最终价值: {results['final_value']:,.2f}元")
            print(f"总收益率: {results['total_return']:.2%}")
            print(f"年化收益率: {results['annual_return']:.2%}")
            print(f"波动率: {results['volatility']:.2%}")
            print(f"夏普比率: {results['sharpe_ratio']:.2f}")
            print(f"最大回撤: {results['max_drawdown']:.2%}")
            print(f"胜率: {results['win_rate']:.2%}")
            print(f"平均盈利: {results['avg_profit']:.2%}")
            print(f"交易次数: {results['total_trades']}")

            # 显示交易记录
            if results['trades']:
                print(f"\n📋 交易记录 (共{len(results['trades'])}笔):")
                for i, trade in enumerate(results['trades'][:5], 1):  # 显示前5笔
                    action_emoji = "🟢" if trade['action'] == 'buy' else "🔴"
                    print(f"  {i}. {action_emoji} {trade['action'].upper()} {trade['symbol']} "
                          f"{trade['shares']}股 @{trade['price']:.2f}元 ({trade['date']})")

                if len(results['trades']) > 5:
                    print(f"  ... 还有{len(results['trades'])-5}笔交易")
            else:
                print("\n📋 无交易记录")

            return True
        else:
            print("❌ 回测失败，未获得结果")
            return False

    except Exception as e:
        print(f"❌ 回测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy():
    """测试策略函数"""
    print("\n🧪 测试策略函数...")

    try:
        import pandas as pd

        # 创建测试数据
        test_daily_data = {
            '000001': {'Close': 10.5, 'High': 10.8, 'Low': 10.2, 'Volume': 1000000}
        }

        test_historical_data = {
            '000001': pd.DataFrame({
                'Close': [10.0, 10.1, 10.2, 10.3, 10.4, 10.5],
                'High': [10.2, 10.3, 10.4, 10.5, 10.6, 10.8],
                'Low': [9.8, 9.9, 10.0, 10.1, 10.2, 10.2],
                'Volume': [1000000] * 6
            })
        }

        # 测试策略
        signals = ma_crossover_strategy(
            test_daily_data,
            test_historical_data,
            {'short_period': 2, 'long_period': 3}
        )

        print(f"✅ 策略测试成功，信号: {signals}")
        return True

    except Exception as e:
        print(f"❌ 策略测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始回测功能测试")
    print("=" * 50)

    # 测试策略函数
    strategy_ok = test_strategy()

    # 测试完整回测
    if strategy_ok:
        backtest_ok = test_backtest()

        if backtest_ok:
            print("\n🎉 所有测试通过！回测功能正常工作。")
        else:
            print("\n⚠️ 回测功能存在问题，请检查数据获取和计算逻辑。")
    else:
        print("\n⚠️ 策略函数存在问题，请检查策略实现。")
