"""
配置文件 - 股票分析系统
"""

# 数据源配置
DATA_SOURCES = {
    'akshare': True,
    'tushare': False,  # 需要token
    'yfinance': True
}

# Tushare配置 (如果使用)
TUSHARE_TOKEN = ""  # 请在此填入你的tushare token

# 行业分类配置
INDUSTRY_MAPPING = {
    '新能源': ['新能源汽车', '光伏', '风电', '储能', '氢能源'],
    '人工智能': ['AI芯片', '算力', '软件', '机器人', '自动驾驶'],
    '医药生物': ['创新药', '医疗器械', '疫苗', 'CXO', '医疗服务'],
    '消费': ['白酒', '食品饮料', '家电', '纺织服装', '零售'],
    '科技': ['半导体', '通信设备', '计算机', '电子元器件', '5G'],
    '金融': ['银行', '保险', '券商', '信托', '租赁'],
    '基建': ['建筑', '建材', '机械', '交通运输', '公用事业'],
    '地产': ['房地产开发', '物业管理', '装修装饰', '园林工程'],
    '资源': ['煤炭', '石油石化', '有色金属', '钢铁', '化工']
}

# 政策关键词映射
POLICY_KEYWORDS = {
    '新能源政策': ['碳中和', '碳达峰', '新能源汽车', '光伏', '风电', '储能'],
    'AI政策': ['人工智能', 'AI', '算力', '数字经济', '智能制造'],
    '医药政策': ['医保', '集采', '创新药', '医疗改革', '生物医药'],
    '消费政策': ['促消费', '内需', '消费升级', '零售', '服务业'],
    '科技政策': ['科技创新', '半导体', '芯片', '5G', '数字化'],
    '金融政策': ['货币政策', '降准', '降息', '金融改革', '资本市场'],
    '基建政策': ['新基建', '基础设施', '投资', '城镇化', '交通'],
    '地产政策': ['房地产', '住房', '城市更新', '保障房', '租赁'],
    '环保政策': ['环保', '节能减排', '绿色发展', '生态保护']
}

# 龙头股票配置 (示例数据，实际应从数据库或API获取)
LEADING_STOCKS = {
    '新能源': {
        '比亚迪': '002594',
        '宁德时代': '300750',
        '隆基绿能': '601012',
        '阳光电源': '300274',
        '亿纬锂能': '300014'
    },
    '人工智能': {
        '科大讯飞': '002230',
        '海康威视': '002415',
        '大华股份': '002236',
        '四维图新': '002405',
        '中科创达': '300496'
    },
    '医药生物': {
        '恒瑞医药': '600276',
        '药明康德': '603259',
        '迈瑞医疗': '300760',
        '智飞生物': '300122',
        '爱尔眼科': '300015'
    },
    '消费': {
        '贵州茅台': '600519',
        '五粮液': '000858',
        '美的集团': '000333',
        '格力电器': '000651',
        '海天味业': '603288'
    },
    '科技': {
        '中芯国际': '688981',
        '韦尔股份': '603501',
        '立讯精密': '002475',
        '歌尔股份': '002241',
        '三安光电': '600703'
    }
}

# 数据更新频率配置
UPDATE_FREQUENCY = {
    'stock_price': '1min',  # 股价数据更新频率
    'industry_data': '1hour',  # 行业数据更新频率
    'policy_news': '30min',  # 政策新闻更新频率
    'financial_data': '1day'  # 财务数据更新频率
}

# 技术指标配置
TECHNICAL_INDICATORS = {
    'ma_periods': [5, 10, 20, 60],  # 移动平均线周期
    'rsi_period': 14,  # RSI周期
    'macd_params': (12, 26, 9),  # MACD参数
    'bollinger_period': 20,  # 布林带周期
    'volume_ma_period': 20  # 成交量移动平均周期
}

# 风险控制配置
RISK_CONTROL = {
    'max_position': 0.1,  # 单只股票最大仓位
    'stop_loss': 0.08,  # 止损比例
    'take_profit': 0.15,  # 止盈比例
    'max_drawdown': 0.05  # 最大回撤
}
