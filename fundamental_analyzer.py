"""
基本面分析模块
"""

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class FundamentalAnalyzer:
    """基本面分析器"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 3600  # 1小时缓存
    
    def get_company_info(self, stock_code: str) -> dict:
        """获取公司基本信息"""
        try:
            # 获取股票基本信息
            stock_info = ak.stock_individual_info_em(symbol=stock_code)
            
            if not stock_info.empty:
                info_dict = {}
                for _, row in stock_info.iterrows():
                    info_dict[row['item']] = row['value']
                
                logger.info(f"获取股票 {stock_code} 基本信息成功")
                return info_dict
            else:
                logger.warning(f"未获取到股票 {stock_code} 基本信息")
                return {}
                
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 基本信息失败: {e}")
            return {}
    
    def get_financial_data(self, stock_code: str) -> dict:
        """获取财务数据"""
        try:
            financial_data = {}
            
            # 获取财务指标
            try:
                financial_indicators = ak.stock_financial_abstract_ths(symbol=stock_code)
                if not financial_indicators.empty:
                    latest_data = financial_indicators.iloc[-1]
                    financial_data['financial_indicators'] = latest_data.to_dict()
            except:
                pass
            
            # 获取利润表
            try:
                profit_data = ak.stock_profit_sheet_by_report_em(symbol=stock_code)
                if not profit_data.empty:
                    latest_profit = profit_data.iloc[-1]
                    financial_data['profit_data'] = latest_profit.to_dict()
            except:
                pass
            
            # 获取资产负债表
            try:
                balance_data = ak.stock_balance_sheet_by_report_em(symbol=stock_code)
                if not balance_data.empty:
                    latest_balance = balance_data.iloc[-1]
                    financial_data['balance_data'] = latest_balance.to_dict()
            except:
                pass
            
            # 获取现金流量表
            try:
                cashflow_data = ak.stock_cash_flow_sheet_by_report_em(symbol=stock_code)
                if not cashflow_data.empty:
                    latest_cashflow = cashflow_data.iloc[-1]
                    financial_data['cashflow_data'] = latest_cashflow.to_dict()
            except:
                pass
            
            logger.info(f"获取股票 {stock_code} 财务数据成功")
            return financial_data
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 财务数据失败: {e}")
            return {}
    
    def calculate_valuation_metrics(self, stock_code: str) -> dict:
        """计算估值指标"""
        try:
            valuation = {}
            
            # 获取实时数据
            realtime_data = ak.stock_zh_a_spot_em()
            stock_data = realtime_data[realtime_data['代码'] == stock_code]
            
            if not stock_data.empty:
                current_price = float(stock_data.iloc[0]['最新价'])
                pe_ratio = float(stock_data.iloc[0].get('市盈率-动态', 0))
                pb_ratio = float(stock_data.iloc[0].get('市净率', 0))
                market_cap = float(stock_data.iloc[0].get('总市值', 0))
                
                valuation.update({
                    'current_price': current_price,
                    'pe_ratio': pe_ratio,
                    'pb_ratio': pb_ratio,
                    'market_cap': market_cap,
                    'market_cap_yi': market_cap / 1e8
                })
                
                # 估值评级
                valuation['pe_rating'] = self._rate_pe_ratio(pe_ratio)
                valuation['pb_rating'] = self._rate_pb_ratio(pb_ratio)
                valuation['market_cap_rating'] = self._rate_market_cap(market_cap)
            
            # 获取历史PE/PB数据进行比较
            try:
                hist_data = ak.stock_zh_a_hist(symbol=stock_code, period="daily", start_date="20230101")
                if not hist_data.empty:
                    # 计算历史平均PE
                    avg_pe = hist_data['市盈率'].mean() if '市盈率' in hist_data.columns else pe_ratio
                    valuation['historical_avg_pe'] = avg_pe
                    valuation['pe_vs_historical'] = (pe_ratio - avg_pe) / avg_pe * 100 if avg_pe > 0 else 0
            except:
                pass
            
            logger.info(f"计算股票 {stock_code} 估值指标成功")
            return valuation
            
        except Exception as e:
            logger.error(f"计算股票 {stock_code} 估值指标失败: {e}")
            return {}
    
    def _rate_pe_ratio(self, pe_ratio: float) -> str:
        """PE比率评级"""
        if pe_ratio <= 0:
            return "无效"
        elif pe_ratio < 15:
            return "低估"
        elif pe_ratio < 25:
            return "合理"
        elif pe_ratio < 40:
            return "偏高"
        else:
            return "高估"
    
    def _rate_pb_ratio(self, pb_ratio: float) -> str:
        """PB比率评级"""
        if pb_ratio <= 0:
            return "无效"
        elif pb_ratio < 1:
            return "低估"
        elif pb_ratio < 2:
            return "合理"
        elif pb_ratio < 3:
            return "偏高"
        else:
            return "高估"
    
    def _rate_market_cap(self, market_cap: float) -> str:
        """市值评级"""
        market_cap_yi = market_cap / 1e8
        if market_cap_yi < 50:
            return "小市值"
        elif market_cap_yi < 200:
            return "中小市值"
        elif market_cap_yi < 500:
            return "中市值"
        elif market_cap_yi < 1000:
            return "大市值"
        else:
            return "超大市值"
    
    def analyze_growth_potential(self, stock_code: str) -> dict:
        """分析成长潜力"""
        try:
            growth_analysis = {
                'revenue_growth': 0,
                'profit_growth': 0,
                'roe_trend': 'stable',
                'debt_ratio': 0,
                'growth_score': 0,
                'growth_rating': '一般'
            }
            
            # 获取财务数据
            financial_data = self.get_financial_data(stock_code)
            
            if financial_data:
                # 分析营收增长
                if 'profit_data' in financial_data:
                    profit_data = financial_data['profit_data']
                    revenue = profit_data.get('营业总收入', 0)
                    if revenue:
                        growth_analysis['revenue_growth'] = float(revenue) if isinstance(revenue, str) else revenue
                
                # 分析盈利增长
                if 'financial_indicators' in financial_data:
                    indicators = financial_data['financial_indicators']
                    roe = indicators.get('净资产收益率', 0)
                    if roe:
                        growth_analysis['roe'] = float(roe) if isinstance(roe, str) else roe
                
                # 计算成长评分
                score = 0
                if growth_analysis['revenue_growth'] > 0.2:  # 营收增长>20%
                    score += 30
                elif growth_analysis['revenue_growth'] > 0.1:
                    score += 20
                elif growth_analysis['revenue_growth'] > 0:
                    score += 10
                
                if growth_analysis.get('roe', 0) > 15:  # ROE>15%
                    score += 25
                elif growth_analysis.get('roe', 0) > 10:
                    score += 15
                elif growth_analysis.get('roe', 0) > 5:
                    score += 10
                
                growth_analysis['growth_score'] = score
                
                # 成长评级
                if score >= 50:
                    growth_analysis['growth_rating'] = '优秀'
                elif score >= 35:
                    growth_analysis['growth_rating'] = '良好'
                elif score >= 20:
                    growth_analysis['growth_rating'] = '一般'
                else:
                    growth_analysis['growth_rating'] = '较差'
            
            logger.info(f"分析股票 {stock_code} 成长潜力成功")
            return growth_analysis
            
        except Exception as e:
            logger.error(f"分析股票 {stock_code} 成长潜力失败: {e}")
            return {}
    
    def get_industry_analysis(self, stock_code: str) -> dict:
        """行业分析"""
        try:
            industry_analysis = {}
            
            # 获取公司信息
            company_info = self.get_company_info(stock_code)
            
            if company_info:
                industry = company_info.get('所属行业', '')
                if industry:
                    industry_analysis['industry'] = industry
                    
                    # 获取同行业公司对比
                    try:
                        industry_stocks = ak.stock_board_industry_name_em()
                        if not industry_stocks.empty:
                            same_industry = industry_stocks[industry_stocks['板块名称'] == industry]
                            if not same_industry.empty:
                                industry_analysis['industry_stock_count'] = len(same_industry)
                                industry_analysis['industry_avg_pe'] = same_industry['市盈率'].mean()
                    except:
                        pass
            
            logger.info(f"获取股票 {stock_code} 行业分析成功")
            return industry_analysis
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 行业分析失败: {e}")
            return {}
    
    def comprehensive_analysis(self, stock_code: str) -> dict:
        """综合基本面分析"""
        try:
            analysis = {
                'stock_code': stock_code,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'company_info': {},
                'valuation': {},
                'growth_potential': {},
                'industry_analysis': {},
                'investment_rating': '中性',
                'risk_level': '中等',
                'target_price': 0,
                'recommendation': []
            }
            
            # 获取各项分析
            analysis['company_info'] = self.get_company_info(stock_code)
            analysis['valuation'] = self.calculate_valuation_metrics(stock_code)
            analysis['growth_potential'] = self.analyze_growth_potential(stock_code)
            analysis['industry_analysis'] = self.get_industry_analysis(stock_code)
            
            # 综合评分
            total_score = 0
            
            # 估值评分 (30%)
            valuation = analysis['valuation']
            if valuation.get('pe_rating') == '低估':
                total_score += 30
            elif valuation.get('pe_rating') == '合理':
                total_score += 20
            elif valuation.get('pe_rating') == '偏高':
                total_score += 10
            
            # 成长性评分 (40%)
            growth = analysis['growth_potential']
            growth_score = growth.get('growth_score', 0)
            total_score += growth_score * 0.4
            
            # 市值评分 (20%) - 偏好小市值
            if valuation.get('market_cap_rating') == '小市值':
                total_score += 20
            elif valuation.get('market_cap_rating') == '中小市值':
                total_score += 15
            elif valuation.get('market_cap_rating') == '中市值':
                total_score += 10
            
            # 行业评分 (10%)
            # 这里可以根据行业热度调整
            total_score += 10
            
            analysis['total_score'] = total_score
            
            # 投资评级
            if total_score >= 80:
                analysis['investment_rating'] = '强烈推荐'
                analysis['risk_level'] = '低'
            elif total_score >= 65:
                analysis['investment_rating'] = '推荐'
                analysis['risk_level'] = '中低'
            elif total_score >= 50:
                analysis['investment_rating'] = '中性'
                analysis['risk_level'] = '中等'
            elif total_score >= 35:
                analysis['investment_rating'] = '谨慎'
                analysis['risk_level'] = '中高'
            else:
                analysis['investment_rating'] = '不推荐'
                analysis['risk_level'] = '高'
            
            # 生成投资建议
            recommendations = []
            
            if analysis['investment_rating'] in ['强烈推荐', '推荐']:
                recommendations.append("✅ 基本面良好，建议关注")
                if valuation.get('pe_rating') == '低估':
                    recommendations.append("💰 估值偏低，存在价值修复机会")
                if growth.get('growth_rating') == '优秀':
                    recommendations.append("📈 成长性优秀，具备长期投资价值")
            
            if valuation.get('market_cap_rating') == '小市值':
                recommendations.append("🚀 小市值股票，具备爆发潜力")
                recommendations.append("⚠️ 注意控制仓位，建议不超过5%")
            
            if analysis['risk_level'] in ['中高', '高']:
                recommendations.append("🛡️ 风险较高，建议设置止损")
                recommendations.append("📊 密切关注技术面信号")
            
            analysis['recommendation'] = recommendations
            
            logger.info(f"完成股票 {stock_code} 综合基本面分析")
            return analysis
            
        except Exception as e:
            logger.error(f"股票 {stock_code} 综合基本面分析失败: {e}")
            return {}
