"""
主应用程序 - 股票数据分析系统
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import logging
from industry_analyzer import IndustryAnalyzer
from technical_analyzer import TechnicalAnalyzer
from backtest_engine import BacktestEngine, ma_crossover_strategy
from data_fetcher import DataFetcher
from fundamental_analyzer import FundamentalAnalyzer
from investment_advisor import InvestmentAdvisor
from deep_research import DeepResearchAnalyzer
from limit_up_predictor import LimitUpPredictor
from limit_up_page import limit_up_prediction_page
from config import *

# 配置页面
st.set_page_config(
    page_title="A股行业轮动分析系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化组件
@st.cache_resource
def init_components():
    return {
        'industry_analyzer': IndustryAnalyzer(),
        'technical_analyzer': TechnicalAnalyzer(),
        'backtest_engine': BacktestEngine(),
        'data_fetcher': DataFetcher(),
        'fundamental_analyzer': FundamentalAnalyzer(),
        'investment_advisor': InvestmentAdvisor(),
        'deep_research': DeepResearchAnalyzer(),
        'limit_up_predictor': LimitUpPredictor()
    }

def main():
    """主函数"""
    st.title("🚀 A股行业轮动分析系统")
    st.markdown("---")

    # 初始化组件
    components = init_components()

    # 侧边栏导航
    st.sidebar.title("📊 功能导航")
    page = st.sidebar.selectbox(
        "选择功能模块",
        ["行业轮动分析", "技术指标分析", "🎯 投资决策分析", "📊 深入研究报告", "策略回测", "实时监控", "政策影响分析", "🚀 封板预测"]
    )

    if page == "行业轮动分析":
        industry_rotation_page(components)
    elif page == "技术指标分析":
        technical_analysis_page(components)
    elif page == "🎯 投资决策分析":
        investment_decision_page(components)
    elif page == "📊 深入研究报告":
        deep_research_page(components)
    elif page == "策略回测":
        backtest_page(components)
    elif page == "实时监控":
        monitoring_page(components)
    elif page == "政策影响分析":
        policy_analysis_page(components)
    elif page == "🚀 封板预测":
        limit_up_prediction_page(components)

def industry_rotation_page(components):
    """行业轮动分析页面"""
    st.header("🔄 行业轮动分析")

    # 将分析参数放在顶部，避免布局移动
    st.subheader("⚙️ 分析参数")

    # 智能行业推荐
    st.write("**🎯 智能行业选择**")

    # 获取实时强势行业推荐
    if st.button("🔄 获取强势行业推荐", help="基于实时数据分析推荐当前强势行业"):
        with st.spinner("正在分析强势行业..."):
            try:
                # 获取行业表现数据
                performance_df = components['industry_analyzer'].calculate_industry_performance()

                if not performance_df.empty:
                    # 检测轮动信号
                    signals = components['industry_analyzer'].detect_rotation_signals(performance_df)
                    strong_industries = signals.get('strong_industries', [])

                    if strong_industries:
                        # 存储推荐的强势行业到session state
                        st.session_state['recommended_industries'] = strong_industries[:10]
                        st.session_state['all_industries'] = performance_df['industry'].tolist()

                        st.success(f"✅ 发现 {len(strong_industries)} 个强势行业")

                        # 显示前5个强势行业及其涨幅
                        st.write("**📈 当前强势行业 (按涨幅排序):**")
                        for i, industry in enumerate(strong_industries[:5], 1):
                            industry_data = performance_df[performance_df['industry'] == industry]
                            if not industry_data.empty:
                                change_pct = industry_data.iloc[0]['change_pct_1d']
                                volume_ratio = industry_data.iloc[0].get('volume_ratio', 1.0)

                                # 根据涨幅和成交量设置emoji
                                if change_pct > 3 and volume_ratio > 1.5:
                                    emoji = "🔥💰"  # 高涨幅+高成交量
                                elif change_pct > 3:
                                    emoji = "🔥"    # 高涨幅
                                elif volume_ratio > 2:
                                    emoji = "💰"    # 高成交量
                                elif change_pct > 1:
                                    emoji = "📈"    # 中等涨幅
                                else:
                                    emoji = "🟢"    # 小幅上涨

                                volume_info = f" [量比:{volume_ratio:.1f}]" if volume_ratio > 1.2 else ""
                                st.write(f"  {i}. {emoji} {industry} ({change_pct:+.2f}%){volume_info}")
                    else:
                        st.warning("⚠️ 当前未发现明显强势行业")
                        st.session_state['recommended_industries'] = []
                else:
                    st.error("❌ 无法获取行业数据")

            except Exception as e:
                st.error(f"❌ 获取强势行业失败: {str(e)}")

    # 行业选择 (智能推荐 + 手动选择)
    if 'recommended_industries' in st.session_state and st.session_state['recommended_industries']:
        # 有推荐行业时，默认选择推荐的强势行业
        recommended = st.session_state['recommended_industries']
        all_industries = st.session_state.get('all_industries', list(INDUSTRY_MAPPING.keys()))

        # 智能推荐模式
        use_recommended = st.checkbox("🎯 使用智能推荐的强势行业", value=True,
                                    help=f"自动选择当前{len(recommended)}个强势行业")

        if use_recommended:
            # 显示推荐的行业标签，可以点击取消选择
            st.write("**选择关注行业**")

            # 初始化选中的行业
            if 'selected_industries_manual' not in st.session_state:
                st.session_state['selected_industries_manual'] = set(recommended)

            # 创建可点击的行业标签
            cols = st.columns(5)  # 每行5个标签
            for i, industry in enumerate(all_industries):
                col_idx = i % 5
                with cols[col_idx]:
                    # 检查是否被选中
                    is_selected = industry in st.session_state['selected_industries_manual']

                    # 创建按钮，选中的显示为红色，未选中的显示为灰色
                    if is_selected:
                        if st.button(f"❌ {industry}", key=f"industry_{i}", help="点击取消选择"):
                            st.session_state['selected_industries_manual'].discard(industry)
                            st.rerun()
                    else:
                        if st.button(f"➕ {industry}", key=f"industry_{i}", help="点击选择"):
                            st.session_state['selected_industries_manual'].add(industry)
                            st.rerun()

            selected_industries = list(st.session_state['selected_industries_manual'])

            if selected_industries:
                st.info(f"✅ 已选择 {len(selected_industries)} 个行业")
            else:
                st.warning("⚠️ 请至少选择一个行业")
        else:
            # 手动选择模式
            selected_industries = st.multiselect(
                "手动选择关注行业",
                all_industries,
                default=recommended[:5],
                help="可以修改或添加其他行业"
            )
    else:
        # 没有推荐时，使用传统选择方式
        st.info("💡 点击上方按钮获取智能推荐，或手动选择行业")
        selected_industries = st.multiselect(
            "选择关注行业",
            list(INDUSTRY_MAPPING.keys()),
            default=list(INDUSTRY_MAPPING.keys())[:5],
            help="建议先获取智能推荐"
        )

    # 其他参数设置
    param_col1, param_col2, param_col3 = st.columns(3)

    with param_col1:
        # 时间范围
        analysis_period = st.selectbox(
            "分析周期",
            ["1天", "3天", "7天", "30天"],
            index=2
        )

    with param_col2:
        # 龙头股票推荐开关
        show_leading_stocks = st.checkbox("🎯 显示小市值龙头股票", value=True, help="智能筛选受资金青睐的小市值龙头股票")
        explosive_mode = st.checkbox("🚀 爆发潜力模式", value=True, help="重点关注最具急速上涨潜力的股票")

    with param_col3:
        # 资金流向监测开关
        fund_flow_mode = st.checkbox("💰 资金流向增强", value=False, help="结合资金流向分析，识别突然资金流入的股票")

    # 显示配置信息
    if selected_industries:
        st.info(f"📅 分析周期: {analysis_period} | 📊 关注行业: {len(selected_industries)}个 | 🎯 龙头股票: {'开启' if show_leading_stocks else '关闭'}")
    else:
        st.warning("⚠️ 请选择要分析的行业")

    # 开始分析按钮
    analysis_disabled = not selected_industries
    analysis_button_text = "🚀 开始行业轮动分析" if selected_industries else "⚠️ 请先选择关注行业"

    # 添加分隔线
    st.markdown("---")

    # 分析结果区域
    st.subheader("📊 分析结果")

    if st.button(analysis_button_text, type="primary", disabled=analysis_disabled):
        with st.spinner("正在分析行业轮动..."):
            try:
                # 获取行业表现数据
                performance_df = components['industry_analyzer'].calculate_industry_performance()

                if not performance_df.empty:
                    # 过滤用户选择的行业
                    if selected_industries:
                        performance_df = performance_df[performance_df['industry'].isin(selected_industries)]

                    if performance_df.empty:
                        st.warning("⚠️ 选择的行业中没有找到数据，请重新选择")
                        return
                    # 1. 行业表现概览
                    st.subheader("📊 行业表现概览")

                    col1, col2, col3, col4 = st.columns(4)

                    total_industries = len(performance_df)
                    rising_count = len(performance_df[performance_df['change_pct_1d'] > 0])
                    falling_count = len(performance_df[performance_df['change_pct_1d'] < 0])
                    avg_change = performance_df['change_pct_1d'].mean()

                    with col1:
                        st.metric("总行业数", total_industries)
                    with col2:
                        st.metric("上涨行业", rising_count, f"{rising_count/total_industries*100:.1f}%")
                    with col3:
                        st.metric("下跌行业", falling_count, f"{falling_count/total_industries*100:.1f}%")
                    with col4:
                        st.metric("平均涨幅", f"{avg_change:.2f}%")

                    # 2. 轮动信号检测
                    st.subheader("🔄 轮动信号检测")

                    signals = components['industry_analyzer'].detect_rotation_signals(performance_df)

                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.write("**🟢 强势行业**")
                        strong_industries = signals.get('strong_industries', [])
                        for industry in strong_industries[:8]:
                            # 获取该行业的涨跌幅
                            industry_data = performance_df[performance_df['industry'] == industry]
                            if not industry_data.empty:
                                change_pct = industry_data.iloc[0]['change_pct_1d']
                                st.write(f"• {industry} ({change_pct:+.2f}%)")
                        if len(strong_industries) > 8:
                            st.write(f"... 还有{len(strong_industries)-8}个")

                    with col2:
                        st.write("**🔴 弱势行业**")
                        weak_industries = signals.get('weak_industries', [])
                        for industry in weak_industries[:8]:
                            industry_data = performance_df[performance_df['industry'] == industry]
                            if not industry_data.empty:
                                change_pct = industry_data.iloc[0]['change_pct_1d']
                                st.write(f"• {industry} ({change_pct:+.2f}%)")
                        if len(weak_industries) > 8:
                            st.write(f"... 还有{len(weak_industries)-8}个")

                    with col3:
                        st.write("**🔄 轮动候选**")
                        rotation_candidates = signals.get('rotation_candidates', [])
                        for industry in rotation_candidates[:8]:
                            industry_data = performance_df[performance_df['industry'] == industry]
                            if not industry_data.empty:
                                change_pct = industry_data.iloc[0]['change_pct_1d']
                                st.write(f"• {industry} ({change_pct:+.2f}%)")
                        if len(rotation_candidates) > 8:
                            st.write(f"... 还有{len(rotation_candidates)-8}个")

                    with col4:
                        st.write("**🛡️ 防御性行业**")
                        defensive_industries = signals.get('defensive_industries', [])
                        for industry in defensive_industries[:8]:
                            industry_data = performance_df[performance_df['industry'] == industry]
                            if not industry_data.empty:
                                change_pct = industry_data.iloc[0]['change_pct_1d']
                                st.write(f"• {industry} ({change_pct:+.2f}%)")
                        if len(defensive_industries) > 8:
                            st.write(f"... 还有{len(defensive_industries)-8}个")

                    # 3. 小市值龙头股票推荐 (全新智能筛选)
                    if show_leading_stocks and strong_industries:
                        if explosive_mode:
                            st.subheader("🚀 爆发潜力小市值龙头股票")
                            st.info("💡 智能筛选受资金青睐的小市值股票 (50-500亿)，具备急速上涨潜力")

                            # 获取最具爆发潜力的股票
                            if fund_flow_mode:
                                st.info("🔄 正在进行资金流向增强分析，可能需要较长时间...")
                                explosive_stocks = components['industry_analyzer'].get_fund_flow_enhanced_stocks(strong_industries, top_n=15)
                            else:
                                explosive_stocks = components['industry_analyzer'].get_explosive_growth_stocks(strong_industries, top_n=15)

                            if not explosive_stocks.empty:
                                # 显示行业分布统计
                                industry_counts = explosive_stocks['所属行业'].value_counts()
                                st.write(f"**📊 涵盖行业分布:** 共 {len(industry_counts)} 个行业")

                                # 显示行业分布
                                industry_info = []
                                for industry, count in industry_counts.items():
                                    industry_info.append(f"{industry}({count}只)")
                                st.info(" | ".join(industry_info))

                                # 显示爆发潜力排行榜
                                st.write("**🏆 爆发潜力排行榜:**")

                                # 创建展示数据
                                display_data = []
                                for i, (_, stock) in enumerate(explosive_stocks.iterrows(), 1):
                                    market_cap_yi = stock['总市值'] / 1e8

                                    # 根据评分设置颜色 (优先使用最终评分)
                                    final_score = stock.get('最终评分', stock.get('综合评分', 0))
                                    if final_score >= 70:
                                        emoji = "🔥"
                                    elif final_score >= 60:
                                        emoji = "⭐"
                                    else:
                                        emoji = "💎"

                                    # 资金流向状态
                                    fund_trend = stock.get('主力资金趋势', 'unknown')
                                    if fund_trend == 'strong_inflow':
                                        fund_emoji = "💰"
                                    elif fund_trend == 'inflow':
                                        fund_emoji = "💵"
                                    elif fund_trend == 'outflow':
                                        fund_emoji = "📉"
                                    else:
                                        fund_emoji = ""

                                    base_data = {
                                        '排名': i,
                                        '股票': f"{emoji}{fund_emoji} {stock['名称']}",
                                        '代码': stock['代码'],
                                        '行业': stock['所属行业'],
                                        '价格': f"{stock['最新价']:.2f}元",
                                        '涨跌幅': f"{stock['涨跌幅']:+.2f}%",
                                        '换手率': f"{stock['换手率']:.2f}%",
                                        '市值': f"{market_cap_yi:.0f}亿",
                                        '评分': f"{final_score:.0f}/100",
                                        '潜力': stock['爆发潜力']
                                    }

                                    # 如果有资金流向数据，添加额外信息
                                    if fund_flow_mode and '资金流向评分' in stock:
                                        base_data.update({
                                            '资金评分': f"{stock['资金流向评分']:.0f}/100",
                                            '资金趋势': stock.get('主力资金趋势', 'unknown')
                                        })

                                    display_data.append(base_data)

                                display_df = pd.DataFrame(display_data)
                                st.dataframe(display_df, use_container_width=True)

                                # 分类展示
                                col1, col2, col3 = st.columns(3)

                                high_potential = explosive_stocks[explosive_stocks['爆发潜力'] == '高']
                                medium_potential = explosive_stocks[explosive_stocks['爆发潜力'] == '中']
                                small_cap = explosive_stocks[explosive_stocks['市值等级'] == '小市值']

                                with col1:
                                    st.metric("🔥 高潜力股票", len(high_potential), f"{len(high_potential)/len(explosive_stocks)*100:.0f}%")
                                    if not high_potential.empty:
                                        for _, stock in high_potential.head(3).iterrows():
                                            st.write(f"• {stock['名称']} ({stock['涨跌幅']:+.2f}%)")

                                with col2:
                                    st.metric("💎 小市值股票", len(small_cap), f"{len(small_cap)/len(explosive_stocks)*100:.0f}%")
                                    if not small_cap.empty:
                                        for _, stock in small_cap.head(3).iterrows():
                                            market_cap_yi = stock['总市值'] / 1e8
                                            st.write(f"• {stock['名称']} ({market_cap_yi:.0f}亿)")

                                with col3:
                                    high_turnover = explosive_stocks[explosive_stocks['资金活跃度'] == '高']
                                    st.metric("🌊 资金活跃", len(high_turnover), f"{len(high_turnover)/len(explosive_stocks)*100:.0f}%")
                                    if not high_turnover.empty:
                                        for _, stock in high_turnover.head(3).iterrows():
                                            st.write(f"• {stock['名称']} ({stock['换手率']:.1f}%)")

                                # 投资建议
                                st.subheader("💡 爆发潜力投资建议")

                                top_3_stocks = explosive_stocks.head(3)
                                rising_stocks = explosive_stocks[explosive_stocks['涨跌幅'] > 0]
                                active_stocks = explosive_stocks[explosive_stocks['换手率'] > 5]

                                recommendations = []

                                if not top_3_stocks.empty:
                                    top_names = ', '.join(top_3_stocks['名称'].tolist())
                                    recommendations.append(f"🎯 **重点关注**: {top_names} - 综合评分最高，具备最强爆发潜力")

                                if not rising_stocks.empty:
                                    rising_count = len(rising_stocks)
                                    recommendations.append(f"📈 **上涨趋势**: {rising_count}只股票当日上涨，趋势向好")

                                if not active_stocks.empty:
                                    active_count = len(active_stocks)
                                    recommendations.append(f"🌊 **资金青睐**: {active_count}只股票换手率>5%，受到资金重点关注")

                                recommendations.append("🚀 **操作策略**: 小市值股票波动较大，建议分批建仓，设置止损")
                                recommendations.append("⚠️ **风险控制**: 单只股票仓位不超过5%，总体小市值仓位不超过30%")

                                for i, rec in enumerate(recommendations, 1):
                                    st.write(f"{i}. {rec}")

                            else:
                                st.warning("⚠️ 当前强势行业中暂未发现符合条件的小市值龙头股票")

                        else:
                            # 传统模式：分行业展示
                            st.subheader("🎯 分行业小市值龙头股票")
                            st.info("💡 按行业展示受资金青睐的小市值龙头股票")

                            for industry in strong_industries[:3]:  # 展示前3个强势行业
                                smart_stocks = components['industry_analyzer'].get_smart_leading_stocks(industry, top_n=5)

                                if not smart_stocks.empty:
                                    st.write(f"**📈 {industry} 行业小市值龙头:**")

                                    # 确保列数匹配，最多3列
                                    display_stocks = smart_stocks.head(3)
                                    num_stocks = len(display_stocks)

                                    if num_stocks > 0:
                                        # 固定使用3列，避免动态列数问题
                                        cols = st.columns(3)
                                        for i, (_, stock) in enumerate(display_stocks.iterrows()):
                                            if i < 3:  # 确保不超过3列
                                                with cols[i]:
                                                    market_cap_yi = stock['总市值'] / 1e8

                                                    # 根据评分设置颜色
                                                    if stock['综合评分'] >= 70:
                                                        emoji = "🔥"
                                                    elif stock['综合评分'] >= 60:
                                                        emoji = "⭐"
                                                    else:
                                                        emoji = "💎"

                                                    st.metric(
                                                        f"{emoji} {stock['名称']}",
                                                        f"{stock['最新价']:.2f}元",
                                                        f"{stock['涨跌幅']:+.2f}%"
                                                    )

                                                    st.write(f"代码: {stock['代码']}")
                                                    st.write(f"市值: {market_cap_yi:.0f}亿")
                                                    st.write(f"换手: {stock['换手率']:.1f}%")
                                                    st.write(f"评分: {stock['综合评分']:.0f}/100")

                                    st.write("---")

                    # 5. 行业表现图表
                    st.subheader("📈 行业表现图表")

                    # 创建行业涨跌幅图表
                    fig = go.Figure()

                    # 添加涨跌幅柱状图
                    colors = ['green' if x > 0 else 'red' for x in performance_df['change_pct_1d']]

                    fig.add_trace(go.Bar(
                        x=performance_df['industry'][:20],  # 显示前20个行业
                        y=performance_df['change_pct_1d'][:20],
                        marker_color=colors[:20],
                        name='涨跌幅'
                    ))

                    fig.update_layout(
                        title="行业涨跌幅排行榜 (前20名)",
                        xaxis_title="行业",
                        yaxis_title="涨跌幅 (%)",
                        height=500,
                        xaxis_tickangle=-45
                    )

                    st.plotly_chart(fig, use_container_width=True)

                else:
                    st.error("❌ 未获取到行业数据，请检查网络连接")

            except Exception as e:
                st.error(f"❌ 分析失败: {str(e)}")
                st.info("💡 请检查网络连接或稍后重试")

def technical_analysis_page(components):
    """技术指标分析页面"""
    st.header("📈 技术指标分析")

    # 股票代码输入
    col1, col2 = st.columns([3, 1])

    with col1:
        stock_symbol = st.text_input(
            "请输入股票代码",
            placeholder="例如: 000001, 600519, 300750",
            help="输入6位A股代码"
        )

    with col2:
        analyze_button = st.button("🔍 开始分析", type="primary")

    if analyze_button and stock_symbol:
        with st.spinner(f"正在分析股票 {stock_symbol}..."):
            try:
                # 技术分析
                analysis_result = components['technical_analyzer'].analyze_stock_technical(stock_symbol)

                if analysis_result:
                    st.success("✅ 技术分析完成！")

                    # 显示基本信息
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("股票代码", stock_symbol)
                    with col2:
                        st.metric("当前价格", f"{analysis_result['current_price']:.2f}")
                    with col3:
                        overall_signal = analysis_result['signals'].get('overall_signal', 'neutral')
                        signal_color = "🟢" if overall_signal == 'bullish' else "🔴" if overall_signal == 'bearish' else "🟡"
                        st.metric("综合信号", f"{signal_color} {overall_signal}")
                    with col4:
                        st.metric("分析时间", analysis_result['timestamp'])

                    # 技术指标表格
                    st.subheader("📊 技术指标")
                    indicators_df = pd.DataFrame([analysis_result['indicators']]).T
                    indicators_df.columns = ['数值']
                    indicators_df.index.name = '指标'
                    st.dataframe(indicators_df, use_container_width=True)

                    # 交易信号
                    st.subheader("🎯 交易信号")
                    signals_df = pd.DataFrame([analysis_result['signals']]).T
                    signals_df.columns = ['信号']
                    signals_df.index.name = '指标类型'

                    # 为信号添加颜色
                    def color_signals(val):
                        if val == 'bullish':
                            return 'background-color: lightgreen'
                        elif val == 'bearish':
                            return 'background-color: lightcoral'
                        elif val in ['overbought', 'oversold']:
                            return 'background-color: lightyellow'
                        else:
                            return 'background-color: lightgray'

                    styled_signals = signals_df.style.applymap(color_signals)
                    st.dataframe(styled_signals, use_container_width=True)

                    # 支撑阻力位
                    if 'support_resistance' in analysis_result:
                        st.subheader("📍 支撑阻力位")
                        sr_data = analysis_result['support_resistance']

                        col1, col2 = st.columns(2)
                        with col1:
                            st.write("**阻力位:**")
                            for level in sr_data.get('resistance_levels', []):
                                st.write(f"• {level:.2f}")

                        with col2:
                            st.write("**支撑位:**")
                            for level in sr_data.get('support_levels', []):
                                st.write(f"• {level:.2f}")

                else:
                    st.error("❌ 无法获取股票数据，请检查股票代码是否正确")

            except Exception as e:
                st.error(f"❌ 技术分析失败: {str(e)}")

def backtest_page(components):
    """策略回测页面"""
    st.header("🔬 策略回测")

    # 回测参数设置
    st.subheader("⚙️ 回测参数设置")

    col1, col2, col3 = st.columns(3)

    with col1:
        # 股票选择
        stock_symbols = st.text_area(
            "股票代码列表",
            placeholder="每行一个股票代码，例如:\n000001\n600519\n300750",
            height=100
        )

        # 初始资金
        initial_capital = st.number_input(
            "初始资金 (元)",
            min_value=10000,
            max_value=10000000,
            value=100000,
            step=10000
        )

    with col2:
        # 回测时间范围
        start_date = st.date_input(
            "开始日期",
            value=datetime.now() - timedelta(days=365)
        )

        end_date = st.date_input(
            "结束日期",
            value=datetime.now()
        )

    with col3:
        # 策略参数
        st.write("**移动平均线策略参数:**")
        short_period = st.number_input("短期MA", min_value=1, max_value=50, value=5)
        long_period = st.number_input("长期MA", min_value=1, max_value=200, value=20)

    # 开始回测
    if st.button("🚀 开始回测", type="primary"):
        if stock_symbols.strip():
            symbols = [s.strip() for s in stock_symbols.strip().split('\n') if s.strip()]

            with st.spinner("正在运行回测..."):
                try:
                    # 设置回测引擎
                    backtest_engine = BacktestEngine(initial_capital)
                    backtest_engine.add_strategy(ma_crossover_strategy, "移动平均线交叉策略")

                    # 运行回测
                    results = backtest_engine.run_backtest(
                        symbols=symbols,
                        start_date=start_date.strftime('%Y-%m-%d'),
                        end_date=end_date.strftime('%Y-%m-%d'),
                        strategy_params={
                            'short_period': short_period,
                            'long_period': long_period
                        }
                    )

                    if results:
                        st.success("✅ 回测完成！")

                        # 显示回测结果
                        st.subheader("📊 回测结果")

                        # 关键指标
                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            st.metric(
                                "总收益率",
                                f"{results['total_return']:.2%}",
                                delta=f"{results['total_return']:.2%}"
                            )

                        with col2:
                            st.metric(
                                "年化收益率",
                                f"{results['annual_return']:.2%}"
                            )

                        with col3:
                            st.metric(
                                "夏普比率",
                                f"{results['sharpe_ratio']:.2f}"
                            )

                        with col4:
                            st.metric(
                                "最大回撤",
                                f"{results['max_drawdown']:.2%}"
                            )

                        # 绘制收益曲线
                        if 'portfolio_values' in results:
                            portfolio_df = pd.DataFrame(results['portfolio_values'])
                            portfolio_df['date'] = pd.to_datetime(portfolio_df['date'])

                            fig = go.Figure()
                            fig.add_trace(go.Scatter(
                                x=portfolio_df['date'],
                                y=portfolio_df['value'],
                                mode='lines',
                                name='组合价值',
                                line=dict(color='blue', width=2)
                            ))

                            fig.add_hline(
                                y=initial_capital,
                                line_dash="dash",
                                line_color="red",
                                annotation_text="初始资金"
                            )

                            fig.update_layout(
                                title="组合价值曲线",
                                xaxis_title="日期",
                                yaxis_title="价值 (元)",
                                height=500
                            )

                            st.plotly_chart(fig, use_container_width=True)

                        # 交易记录
                        if 'trades' in results and results['trades']:
                            st.subheader("📋 交易记录")
                            trades_df = pd.DataFrame(results['trades'])
                            st.dataframe(trades_df, use_container_width=True)

                        # 详细统计
                        st.subheader("📈 详细统计")
                        stats_data = {
                            '指标': ['胜率', '平均盈利', '交易次数', '波动率', '最终价值'],
                            '数值': [
                                f"{results['win_rate']:.2%}",
                                f"{results['avg_profit']:.2%}",
                                results['total_trades'],
                                f"{results['volatility']:.2%}",
                                f"{results['final_value']:.2f}"
                            ]
                        }
                        stats_df = pd.DataFrame(stats_data)
                        st.dataframe(stats_df, use_container_width=True)

                    else:
                        st.error("❌ 回测失败，请检查股票代码和日期范围")

                except Exception as e:
                    st.error(f"❌ 回测过程中出现错误: {str(e)}")
        else:
            st.warning("⚠️ 请输入至少一个股票代码")

def monitoring_page(components):
    """实时监控页面"""
    st.header("📡 实时监控")

    # 创建实时刷新按钮
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("🔄 刷新数据", type="primary"):
            st.rerun()

    with col2:
        auto_refresh = st.checkbox("⏰ 自动刷新", value=False)

    with col3:
        st.info("💡 数据每30秒自动更新一次")

    # 获取实时数据
    industry_analyzer = components['industry_analyzer']
    data_fetcher = components['data_fetcher']

    try:
        # 1. 市场概览
        st.subheader("📊 市场概览")

        col1, col2, col3, col4 = st.columns(4)

        # 获取行业数据
        performance_df = industry_analyzer.calculate_industry_performance()

        if not performance_df.empty:
            # 统计数据
            total_industries = len(performance_df)
            rising_industries = len(performance_df[performance_df['change_pct_1d'] > 0])
            falling_industries = len(performance_df[performance_df['change_pct_1d'] < 0])
            avg_change = performance_df['change_pct_1d'].mean()

            with col1:
                st.metric("总行业数", total_industries)

            with col2:
                st.metric("上涨行业", rising_industries, f"{rising_industries/total_industries*100:.1f}%")

            with col3:
                st.metric("下跌行业", falling_industries, f"{falling_industries/total_industries*100:.1f}%")

            with col4:
                st.metric("平均涨幅", f"{avg_change:.2f}%", f"{avg_change:.2f}%")

        # 2. 实时行业排行
        st.subheader("🏆 实时行业排行")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**📈 涨幅榜前10**")
            if not performance_df.empty:
                top_10 = performance_df.head(10)[['industry', 'change_pct_1d', 'market_cap']]
                top_10['market_cap'] = top_10['market_cap'].apply(lambda x: f"{x/1e8:.0f}亿" if x > 0 else "N/A")
                top_10.columns = ['行业', '涨跌幅(%)', '总市值']
                st.dataframe(top_10, use_container_width=True)

        with col2:
            st.write("**📉 跌幅榜前10**")
            if not performance_df.empty:
                bottom_10 = performance_df.tail(10)[['industry', 'change_pct_1d', 'market_cap']]
                bottom_10['market_cap'] = bottom_10['market_cap'].apply(lambda x: f"{x/1e8:.0f}亿" if x > 0 else "N/A")
                bottom_10.columns = ['行业', '涨跌幅(%)', '总市值']
                st.dataframe(bottom_10, use_container_width=True)

        # 3. 轮动信号监控
        st.subheader("🔄 轮动信号监控")

        signals = industry_analyzer.detect_rotation_signals(performance_df)

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.write("**🟢 强势行业**")
            strong_industries = signals.get('strong_industries', [])
            for industry in strong_industries[:5]:
                st.write(f"• {industry}")
            if len(strong_industries) > 5:
                st.write(f"... 还有{len(strong_industries)-5}个")

        with col2:
            st.write("**🔴 弱势行业**")
            weak_industries = signals.get('weak_industries', [])
            for industry in weak_industries[:5]:
                st.write(f"• {industry}")
            if len(weak_industries) > 5:
                st.write(f"... 还有{len(weak_industries)-5}个")

        with col3:
            st.write("**🔄 轮动候选**")
            rotation_candidates = signals.get('rotation_candidates', [])
            for industry in rotation_candidates[:5]:
                st.write(f"• {industry}")
            if len(rotation_candidates) > 5:
                st.write(f"... 还有{len(rotation_candidates)-5}个")

        with col4:
            st.write("**🛡️ 防御性行业**")
            defensive_industries = signals.get('defensive_industries', [])
            for industry in defensive_industries[:5]:
                st.write(f"• {industry}")
            if len(defensive_industries) > 5:
                st.write(f"... 还有{len(defensive_industries)-5}个")

        # 4. 实时图表
        st.subheader("📈 实时图表")

        if not performance_df.empty:
            # 创建涨跌分布图
            fig = go.Figure()

            # 添加涨跌幅分布
            fig.add_trace(go.Histogram(
                x=performance_df['change_pct_1d'],
                nbinsx=20,
                name='行业涨跌幅分布',
                marker_color='skyblue'
            ))

            fig.update_layout(
                title="行业涨跌幅分布",
                xaxis_title="涨跌幅 (%)",
                yaxis_title="行业数量",
                height=400
            )

            st.plotly_chart(fig, use_container_width=True)

        # 5. 市场情绪指标
        st.subheader("💭 市场情绪指标")

        try:
            sentiment_data = data_fetcher.get_market_sentiment()
            if sentiment_data:
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("数据源", "多维度")

                with col2:
                    st.metric("情绪指标", len(sentiment_data))

                with col3:
                    st.metric("更新状态", "实时")
            else:
                st.info("💡 市场情绪数据获取中...")
        except Exception as e:
            st.warning(f"⚠️ 市场情绪数据暂时不可用: {str(e)}")

        # 6. 资金流向突然变化监测
        st.subheader("💰 资金流向突然变化监测")

        if st.button("🔍 检测突然资金流入", help="监测市场中突然出现大量资金流入的股票"):
            with st.spinner("正在监测资金流向变化..."):
                try:
                    sudden_fund_analysis = components['industry_analyzer'].monitor_sudden_fund_inflow()

                    if sudden_fund_analysis:
                        summary = sudden_fund_analysis.get('summary', {})

                        col1, col2, col3 = st.columns(3)

                        with col1:
                            st.metric("突然流入", summary.get('sudden_inflow_count', 0), "只股票")

                        with col2:
                            st.metric("突然流出", summary.get('sudden_outflow_count', 0), "只股票")

                        with col3:
                            st.metric("异常活跃", summary.get('abnormal_activity_count', 0), "只股票")

                        # 显示预警信息
                        alerts = sudden_fund_analysis.get('alerts', [])
                        for alert in alerts:
                            if alert['type'] == 'sudden_inflow':
                                st.success(f"🔥 {alert['message']}")

                                # 显示具体股票
                                if alert['stocks']:
                                    st.write("**突然资金流入股票:**")
                                    for stock in alert['stocks'][:5]:
                                        signals_text = '; '.join(stock.get('signals', [])[:2])
                                        st.write(f"• {stock['stock_code']} (评分: {stock['score']}/100) - {signals_text}")

                            elif alert['type'] == 'abnormal_activity':
                                st.warning(f"⚡ {alert['message']}")

                        # 显示投资建议
                        recommendations = sudden_fund_analysis.get('recommendations', [])
                        if recommendations:
                            st.write("**💡 基于资金流向的投资建议:**")
                            for i, rec in enumerate(recommendations, 1):
                                st.write(f"{i}. {rec}")
                    else:
                        st.info("💡 当前未检测到显著的资金流向变化")

                except Exception as e:
                    st.error(f"❌ 资金流向监测失败: {str(e)}")

        # 自动刷新逻辑
        if auto_refresh:
            import time
            time.sleep(30)
            st.rerun()

    except Exception as e:
        st.error(f"❌ 获取实时数据失败: {str(e)}")
        st.info("💡 请检查网络连接或稍后重试")

def policy_analysis_page(components):
    """政策影响分析页面"""
    st.header("📜 政策影响分析")

    industry_analyzer = components['industry_analyzer']

    # 政策关键词选择
    st.subheader("🎯 政策关键词设置")

    col1, col2 = st.columns([2, 1])

    with col1:
        # 预设政策关键词
        default_keywords = ['新能源', '人工智能', '医药', '消费', '科技', '金融', '基建', '地产']
        selected_keywords = st.multiselect(
            "选择关注的政策领域",
            default_keywords,
            default=['新能源', '人工智能', '医药'],
            help="选择您想要分析政策影响的领域"
        )

        # 自定义关键词
        custom_keywords = st.text_input(
            "自定义关键词",
            placeholder="输入其他关键词，用逗号分隔",
            help="例如: 碳中和,数字经济,乡村振兴"
        )

        if custom_keywords:
            custom_list = [kw.strip() for kw in custom_keywords.split(',') if kw.strip()]
            selected_keywords.extend(custom_list)

    with col2:
        st.info("💡 政策分析说明\n\n"
                "• 基于新闻数据分析政策影响\n"
                "• 评估对相关行业的影响程度\n"
                "• 提供投资建议和风险提示")

    # 开始分析按钮
    if st.button("🔍 开始政策影响分析", type="primary") and selected_keywords:

        with st.spinner("正在分析政策影响..."):
            try:
                # 1. 政策影响分析
                st.subheader("📊 政策影响分析结果")

                policy_impact = industry_analyzer.analyze_policy_impact(selected_keywords)

                if policy_impact:
                    # 创建影响评分图表
                    keywords = list(policy_impact.keys())
                    impact_scores = [policy_impact[kw].get('impact_score', 0) for kw in keywords]
                    news_counts = [policy_impact[kw].get('news_count', 0) for kw in keywords]

                    if keywords:
                        col1, col2 = st.columns(2)

                        with col1:
                            # 影响评分图
                            fig1 = go.Figure(data=[
                                go.Bar(x=keywords, y=impact_scores, name='影响评分')
                            ])
                            fig1.update_layout(
                                title="政策影响评分",
                                xaxis_title="政策关键词",
                                yaxis_title="影响评分",
                                height=400
                            )
                            st.plotly_chart(fig1, use_container_width=True)

                        with col2:
                            # 新闻数量图
                            fig2 = go.Figure(data=[
                                go.Bar(x=keywords, y=news_counts, name='新闻数量', marker_color='orange')
                            ])
                            fig2.update_layout(
                                title="相关新闻数量",
                                xaxis_title="政策关键词",
                                yaxis_title="新闻数量",
                                height=400
                            )
                            st.plotly_chart(fig2, use_container_width=True)

                    # 2. 详细影响分析
                    st.subheader("📋 详细影响分析")

                    for keyword, impact_data in policy_impact.items():
                        with st.expander(f"🔍 {keyword} 政策影响详情"):
                            col1, col2, col3 = st.columns(3)

                            with col1:
                                st.metric("相关新闻", f"{impact_data.get('news_count', 0)}条")

                            with col2:
                                impact_score = impact_data.get('impact_score', 0)
                                st.metric("影响评分", f"{impact_score:.2f}", f"{impact_score*100:.0f}%")

                            with col3:
                                affected_count = len(impact_data.get('affected_industries', []))
                                st.metric("影响行业", f"{affected_count}个")

                            # 受影响行业
                            affected_industries = impact_data.get('affected_industries', [])
                            if affected_industries:
                                st.write("**受影响行业:**")
                                for industry in affected_industries:
                                    st.write(f"• {industry}")

                            # 最新新闻
                            latest_news = impact_data.get('latest_news', [])
                            if latest_news:
                                st.write("**最新相关新闻:**")
                                for i, news in enumerate(latest_news[:3], 1):
                                    st.write(f"{i}. {news.get('title', 'N/A')}")

                # 3. 行业影响矩阵
                st.subheader("🎯 行业影响矩阵")

                # 获取当前行业表现
                performance_df = industry_analyzer.calculate_industry_performance()

                if not performance_df.empty and policy_impact:
                    # 创建影响矩阵
                    matrix_data = []

                    for keyword, impact_data in policy_impact.items():
                        affected_industries = impact_data.get('affected_industries', [])
                        impact_score = impact_data.get('impact_score', 0)

                        for industry in affected_industries:
                            # 查找行业当前表现
                            industry_perf = performance_df[performance_df['industry'] == industry]
                            if not industry_perf.empty:
                                current_change = industry_perf.iloc[0]['change_pct_1d']
                                matrix_data.append({
                                    '政策关键词': keyword,
                                    '影响行业': industry,
                                    '政策影响评分': impact_score,
                                    '当前涨跌幅(%)': current_change,
                                    '综合评级': '积极' if impact_score > 0.5 and current_change > 0 else
                                               '中性' if impact_score > 0.3 else '观望'
                                })

                    if matrix_data:
                        matrix_df = pd.DataFrame(matrix_data)
                        st.dataframe(matrix_df, use_container_width=True)
                    else:
                        st.info("💡 暂无匹配的行业影响数据")

                # 4. 投资建议
                st.subheader("💡 基于政策分析的投资建议")

                recommendations = []

                # 基于政策影响生成建议
                high_impact_policies = [k for k, v in policy_impact.items() if v.get('impact_score', 0) > 0.5]
                medium_impact_policies = [k for k, v in policy_impact.items() if 0.3 <= v.get('impact_score', 0) <= 0.5]

                if high_impact_policies:
                    recommendations.append(
                        f"🎯 **重点关注**: {', '.join(high_impact_policies)} 相关政策影响较大，"
                        f"建议重点关注相关行业的投资机会。"
                    )

                if medium_impact_policies:
                    recommendations.append(
                        f"⚠️ **适度关注**: {', '.join(medium_impact_policies)} 相关政策影响适中，"
                        f"可适度配置相关行业。"
                    )

                recommendations.append(
                    "📈 **策略建议**: 政策驱动的行业轮动通常具有持续性，"
                    "建议结合技术分析确定具体的买入时机。"
                )

                recommendations.append(
                    "🛡️ **风险提示**: 政策影响存在不确定性，"
                    "建议分散投资，控制单一政策主题的仓位比例。"
                )

                for i, rec in enumerate(recommendations, 1):
                    st.write(f"{i}. {rec}")

                else:
                    st.warning("⚠️ 未获取到政策影响数据，请检查关键词设置或网络连接")

            except Exception as e:
                st.error(f"❌ 政策影响分析失败: {str(e)}")
                st.info("💡 请检查网络连接或稍后重试")

    elif not selected_keywords:
        st.warning("⚠️ 请至少选择一个政策关键词进行分析")

    # 政策分析说明
    st.subheader("📖 政策分析方法说明")

    with st.expander("点击查看分析方法详情"):
        st.write("""
        **数据来源:**
        - 财经新闻数据
        - 政策公告信息
        - 行业研究报告

        **分析方法:**
        1. **关键词匹配**: 基于政策关键词搜索相关新闻
        2. **影响评估**: 根据新闻数量和内容评估影响程度
        3. **行业映射**: 将政策影响映射到具体行业
        4. **综合评分**: 结合多维度数据生成影响评分

        **评分标准:**
        - 0.7-1.0: 高影响 (重点关注)
        - 0.4-0.7: 中等影响 (适度关注)
        - 0.0-0.4: 低影响 (观望)

        **使用建议:**
        - 结合技术分析确定买入时机
        - 关注政策落地的具体进展
        - 分散投资降低政策风险
        """)

def investment_decision_page(components):
    """投资决策分析页面"""
    st.header("🎯 投资决策分析")
    st.markdown("基于基本面、技术面、资金流向的综合投资决策支持")

    # 用户风险偏好设置
    st.subheader("👤 投资者画像设置")
    col1, col2, col3 = st.columns(3)

    with col1:
        risk_tolerance = st.selectbox(
            "风险承受能力",
            ["conservative", "moderate", "aggressive"],
            index=1,
            format_func=lambda x: {"conservative": "保守型", "moderate": "稳健型", "aggressive": "激进型"}[x]
        )

    with col2:
        portfolio_size = st.number_input("投资组合总金额(万元)", min_value=1, max_value=10000, value=10) * 10000

    with col3:
        investment_horizon = st.selectbox("投资期限", ["短期(1-3月)", "中期(3-12月)", "长期(1年以上)"])

    user_profile = {
        'risk_tolerance': risk_tolerance,
        'portfolio_size': portfolio_size,
        'investment_horizon': investment_horizon
    }

    # 股票选择
    st.subheader("📊 股票分析")

    # 可以从推荐列表中选择，或手动输入
    analysis_mode = st.radio("分析模式", ["从推荐列表选择", "手动输入股票代码"])

    if analysis_mode == "从推荐列表选择":
        # 获取推荐股票列表
        if st.button("🔄 获取最新推荐股票"):
            with st.spinner("正在获取推荐股票..."):
                try:
                    # 获取强势行业的爆发潜力股票
                    performance_df = components['industry_analyzer'].calculate_industry_performance()
                    signals = components['industry_analyzer'].detect_rotation_signals(performance_df)
                    strong_industries = signals.get('strong_industries', [])

                    if strong_industries:
                        explosive_stocks = components['industry_analyzer'].get_explosive_growth_stocks(strong_industries, top_n=20)

                        if not explosive_stocks.empty:
                            st.success(f"✅ 获取到 {len(explosive_stocks)} 只推荐股票")

                            # 显示推荐列表供选择
                            stock_options = {}
                            for _, stock in explosive_stocks.iterrows():
                                display_name = f"{stock['名称']}({stock['代码']}) - 评分:{stock['综合评分']:.0f}/100"
                                stock_options[display_name] = stock['代码']

                            selected_display = st.selectbox("选择要分析的股票", list(stock_options.keys()))
                            selected_stock = stock_options[selected_display]

                            st.session_state['selected_stock'] = selected_stock
                        else:
                            st.warning("⚠️ 当前未找到推荐股票")
                    else:
                        st.warning("⚠️ 当前未找到强势行业")
                except Exception as e:
                    st.error(f"❌ 获取推荐股票失败: {str(e)}")
    else:
        # 手动输入
        manual_stock = st.text_input("请输入股票代码 (如: 000001)", value="000001")
        if st.button("确认分析"):
            st.session_state['selected_stock'] = manual_stock

    # 执行投资决策分析
    if 'selected_stock' in st.session_state and st.session_state['selected_stock']:
        stock_code = st.session_state['selected_stock']

        if st.button("🚀 开始投资决策分析", type="primary"):
            with st.spinner("正在进行全面投资决策分析..."):
                try:
                    # 1. 基本面分析
                    st.subheader("📊 基本面分析")
                    fundamental_analysis = components['fundamental_analyzer'].comprehensive_analysis(stock_code)

                    if fundamental_analysis:
                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            investment_rating = fundamental_analysis.get('investment_rating', '中性')
                            rating_color = {
                                '强烈推荐': 'success',
                                '推荐': 'success',
                                '中性': 'warning',
                                '谨慎': 'error',
                                '不推荐': 'error'
                            }.get(investment_rating, 'info')

                            st.metric("投资评级", investment_rating)

                        with col2:
                            total_score = fundamental_analysis.get('total_score', 0)
                            st.metric("基本面评分", f"{total_score:.1f}/100")

                        with col3:
                            risk_level = fundamental_analysis.get('risk_level', '中等')
                            st.metric("风险等级", risk_level)

                        with col4:
                            valuation = fundamental_analysis.get('valuation', {})
                            current_price = valuation.get('current_price', 0)
                            st.metric("当前价格", f"{current_price:.2f}元")

                        # 显示投资建议
                        recommendations = fundamental_analysis.get('recommendation', [])
                        if recommendations:
                            st.write("**💡 基本面投资建议:**")
                            for rec in recommendations:
                                st.write(f"• {rec}")

                    # 2. 技术面分析
                    st.subheader("📈 技术面分析")
                    technical_analysis = components['technical_analyzer'].analyze_stock_technical(stock_code)

                    if technical_analysis:
                        signals = technical_analysis.get('signals', {})
                        overall_signal = signals.get('overall_signal', 'neutral')

                        col1, col2, col3 = st.columns(3)

                        with col1:
                            signal_emoji = {
                                'buy': '🟢 买入',
                                'bullish': '🟡 看涨',
                                'hold': '🔵 持有',
                                'neutral': '⚪ 中性',
                                'bearish': '🟠 看跌',
                                'sell': '🔴 卖出'
                            }.get(overall_signal, '⚪ 中性')
                            st.metric("技术信号", signal_emoji)

                        with col2:
                            indicators = technical_analysis.get('indicators', {})
                            rsi = indicators.get('RSI', 50)
                            st.metric("RSI", f"{rsi:.1f}")

                        with col3:
                            macd = indicators.get('MACD', 0)
                            st.metric("MACD", f"{macd:.4f}")

                    # 3. 资金流向分析
                    st.subheader("💰 资金流向分析")
                    fund_flow_analysis = components['industry_analyzer'].fund_monitor.analyze_fund_flow_signals(stock_code)

                    if fund_flow_analysis:
                        col1, col2, col3 = st.columns(3)

                        with col1:
                            main_trend = fund_flow_analysis.get('main_fund_trend', 'neutral')
                            trend_emoji = {
                                'strong_inflow': '💰 大量流入',
                                'inflow': '💵 流入',
                                'neutral': '⚖️ 平衡',
                                'outflow': '📉 流出'
                            }.get(main_trend, '⚖️ 平衡')
                            st.metric("主力资金", trend_emoji)

                        with col2:
                            fund_score = fund_flow_analysis.get('fund_flow_score', 0)
                            st.metric("资金流向评分", f"{fund_score}/100")

                        with col3:
                            big_deal_activity = fund_flow_analysis.get('big_deal_activity', 'normal')
                            activity_emoji = {
                                'very_active': '🔥 极度活跃',
                                'active': '⚡ 活跃',
                                'normal': '📊 正常',
                                'quiet': '😴 平静'
                            }.get(big_deal_activity, '📊 正常')
                            st.metric("大单活跃度", activity_emoji)

                    # 4. 综合投资决策
                    st.subheader("🎯 综合投资决策")

                    decision = components['investment_advisor'].generate_investment_decision(
                        fundamental_analysis, technical_analysis, fund_flow_analysis, user_profile
                    )

                    if decision:
                        # 决策结果展示
                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            action = decision.get('action', 'hold')
                            action_emoji = {
                                'buy': '🟢 买入',
                                'hold': '🟡 观察',
                                'sell': '🔴 卖出',
                                'avoid': '❌ 避免'
                            }.get(action, '🟡 观察')
                            st.metric("投资决策", action_emoji)

                        with col2:
                            confidence = decision.get('confidence', 0)
                            st.metric("决策置信度", f"{confidence:.1f}%")

                        with col3:
                            position_percentage = decision.get('position_percentage', '0%')
                            st.metric("建议仓位", position_percentage)

                        with col4:
                            target_price = decision.get('target_price', 0)
                            st.metric("目标价位", f"{target_price:.2f}元")

                        # 详细投资建议
                        detailed_advice = decision.get('detailed_advice', [])
                        if detailed_advice:
                            st.write("**📋 详细投资建议:**")
                            for advice in detailed_advice:
                                st.markdown(advice)

                        # 风险评估
                        risk_assessment = decision.get('risk_assessment', {})
                        if risk_assessment:
                            st.write("**⚠️ 风险评估:**")
                            risk_factors = risk_assessment.get('risk_factors', [])
                            if risk_factors:
                                for factor in risk_factors:
                                    st.write(f"• {factor}")
                            else:
                                st.write("• 暂未发现重大风险因素")

                except Exception as e:
                    st.error(f"❌ 投资决策分析失败: {str(e)}")

def deep_research_page(components):
    """深入研究报告页面"""
    st.header("📊 深入研究报告")
    st.markdown("提供公司新闻、分析师评级、股东结构、竞争分析等深度研究")

    # 股票选择
    stock_code = st.text_input("请输入股票代码", value="000001", help="例如: 000001, 300750")

    if st.button("🔍 生成深入研究报告", type="primary"):
        if stock_code:
            with st.spinner("正在生成深入研究报告..."):
                try:
                    report = components['deep_research'].comprehensive_research_report(stock_code)

                    if report:
                        # 执行摘要
                        st.subheader("📋 执行摘要")
                        st.info(report.get('executive_summary', ''))

                        # 投资亮点和关注点
                        col1, col2 = st.columns(2)

                        with col1:
                            st.subheader("✨ 投资亮点")
                            highlights = report.get('investment_highlights', [])
                            if highlights:
                                for highlight in highlights:
                                    st.success(f"✅ {highlight}")
                            else:
                                st.write("暂无明显投资亮点")

                        with col2:
                            st.subheader("⚠️ 关键关注点")
                            concerns = report.get('key_concerns', [])
                            if concerns:
                                for concern in concerns:
                                    st.warning(f"⚠️ {concern}")
                            else:
                                st.write("暂无重大关注点")

                        # 公司新闻
                        st.subheader("📰 最新公司新闻")
                        news_list = report.get('company_news', [])
                        if news_list:
                            for i, news in enumerate(news_list[:5], 1):
                                sentiment_emoji = {
                                    '正面': '🟢',
                                    '负面': '🔴',
                                    '中性': '🟡'
                                }.get(news.get('sentiment', '中性'), '🟡')

                                with st.expander(f"{sentiment_emoji} {news.get('title', '')}"):
                                    st.write(f"**发布时间:** {news.get('publish_time', '')}")
                                    st.write(f"**新闻来源:** {news.get('source', '')}")
                                    st.write(f"**情感倾向:** {news.get('sentiment', '中性')}")
                                    if news.get('content'):
                                        st.write(f"**内容摘要:** {news['content'][:200]}...")
                        else:
                            st.write("暂无相关新闻")

                        # 分析师评级
                        st.subheader("👨‍💼 分析师评级")
                        analyst_ratings = report.get('analyst_ratings', {})
                        if analyst_ratings:
                            col1, col2 = st.columns(2)

                            with col1:
                                avg_rating = analyst_ratings.get('average_rating', '中性')
                                st.metric("平均评级", avg_rating)

                            with col2:
                                target_price = analyst_ratings.get('target_price', 0)
                                if target_price > 0:
                                    st.metric("目标价", f"{target_price:.2f}元")

                            # 评级分布
                            rating_dist = analyst_ratings.get('rating_distribution', {})
                            if rating_dist:
                                st.write("**评级分布:**")
                                for rating, count in rating_dist.items():
                                    st.write(f"• {rating}: {count}家机构")

                        # 股东结构
                        st.subheader("👥 股东结构分析")
                        shareholder_analysis = report.get('shareholder_analysis', {})
                        if shareholder_analysis:
                            concentration = shareholder_analysis.get('ownership_concentration', 0)
                            st.metric("前五大股东持股比例", f"{concentration:.2f}%")

                            major_shareholders = shareholder_analysis.get('major_shareholders', [])
                            if major_shareholders:
                                st.write("**十大股东:**")
                                for i, holder in enumerate(major_shareholders[:5], 1):
                                    st.write(f"{i}. {holder.get('name', '')} - {holder.get('percentage', 0):.2f}%")

                        # 竞争对手分析
                        st.subheader("🏢 竞争对手分析")
                        competitive_analysis = report.get('competitive_analysis', {})
                        competitors = competitive_analysis.get('competitors', [])
                        if competitors:
                            st.write("**主要竞争对手:**")
                            for competitor in competitors[:3]:
                                name = competitor.get('name', '')
                                code = competitor.get('code', '')
                                market_cap = competitor.get('market_cap', 0) / 1e8
                                st.write(f"• {name}({code}) - 市值: {market_cap:.0f}亿")

                        # 风险因素
                        st.subheader("⚠️ 风险因素分析")
                        risk_factors = report.get('risk_factors', {})
                        overall_risk = risk_factors.get('overall_risk_level', '中等风险')
                        st.metric("整体风险等级", overall_risk)

                        risk_categories = ['business_risks', 'financial_risks', 'market_risks', 'regulatory_risks']
                        risk_names = ['经营风险', '财务风险', '市场风险', '监管风险']

                        for category, name in zip(risk_categories, risk_names):
                            risks = risk_factors.get(category, [])
                            if risks:
                                st.write(f"**{name}:**")
                                for risk in risks:
                                    st.write(f"• {risk}")

                        # 研究结论
                        st.subheader("📝 研究结论")
                        conclusion = report.get('research_conclusion', '')
                        if conclusion:
                            st.success(conclusion)

                    else:
                        st.error("❌ 生成研究报告失败")

                except Exception as e:
                    st.error(f"❌ 生成研究报告失败: {str(e)}")
        else:
            st.warning("⚠️ 请输入股票代码")

if __name__ == "__main__":
    main()
