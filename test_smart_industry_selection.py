"""
测试智能行业选择功能
"""

import pandas as pd
from industry_analyzer import IndustryAnalyzer

def test_smart_industry_recommendation():
    """测试智能行业推荐功能"""
    print('🎯 智能行业推荐功能测试')
    print('='*50)

    analyzer = IndustryAnalyzer()

    try:
        # 1. 获取行业表现数据
        print('📊 获取行业表现数据...')
        performance_df = analyzer.calculate_industry_performance()

        if not performance_df.empty:
            print(f'✅ 获取到 {len(performance_df)} 个行业数据')

            # 显示前10个行业的基本信息
            print('\n📈 行业表现概览 (前10名):')
            top_10 = performance_df.head(10)
            for i, (_, row) in enumerate(top_10.iterrows(), 1):
                change_pct = row['change_pct_1d']
                volume_ratio = row.get('volume_ratio', 1.0)

                # 设置emoji
                if change_pct > 3 and volume_ratio > 1.5:
                    emoji = "🔥💰"
                elif change_pct > 3:
                    emoji = "🔥"
                elif volume_ratio > 2:
                    emoji = "💰"
                elif change_pct > 1:
                    emoji = "📈"
                elif change_pct > 0:
                    emoji = "🟢"
                else:
                    emoji = "🔴"

                volume_info = f" [量比:{volume_ratio:.1f}]" if volume_ratio > 1.2 else ""
                print(f'  {i:2d}. {emoji} {row["industry"]:<15} ({change_pct:+6.2f}%){volume_info}')

            # 2. 检测轮动信号
            print('\n🔄 检测行业轮动信号...')
            signals = analyzer.detect_rotation_signals(performance_df)

            strong_industries = signals.get('strong_industries', [])
            weak_industries = signals.get('weak_industries', [])
            rotation_candidates = signals.get('rotation_candidates', [])
            defensive_industries = signals.get('defensive_industries', [])

            print(f'✅ 轮动信号检测完成')
            print(f'   🟢 强势行业: {len(strong_industries)}个')
            print(f'   🔴 弱势行业: {len(weak_industries)}个')
            print(f'   🔄 轮动候选: {len(rotation_candidates)}个')
            print(f'   🛡️ 防御性行业: {len(defensive_industries)}个')

            # 3. 智能推荐强势行业
            if strong_industries:
                print(f'\n🎯 智能推荐强势行业 (前{min(10, len(strong_industries))}个):')

                for i, industry in enumerate(strong_industries[:10], 1):
                    # 获取该行业的详细数据
                    industry_data = performance_df[performance_df['industry'] == industry]
                    if not industry_data.empty:
                        row = industry_data.iloc[0]
                        change_pct = row['change_pct_1d']
                        volume_ratio = row.get('volume_ratio', 1.0)

                        # 推荐理由
                        reasons = []
                        if change_pct > 5:
                            reasons.append("大幅上涨")
                        elif change_pct > 2:
                            reasons.append("明显上涨")

                        if volume_ratio > 2:
                            reasons.append("成交放量")
                        elif volume_ratio > 1.5:
                            reasons.append("成交活跃")

                        if change_pct > 0 and volume_ratio > 1.2:
                            reasons.append("量价齐升")

                        reason_text = "、".join(reasons) if reasons else "相对强势"

                        # 设置推荐等级
                        if change_pct > 3 and volume_ratio > 1.5:
                            level = "🔥 强烈推荐"
                        elif change_pct > 2 or volume_ratio > 1.8:
                            level = "⭐ 推荐"
                        else:
                            level = "💡 关注"

                        print(f'  {i:2d}. {level} {industry}')
                        print(f'      📊 涨幅: {change_pct:+.2f}%  💰 量比: {volume_ratio:.1f}')
                        print(f'      💡 理由: {reason_text}')
                        print()

                # 4. 生成投资建议
                print('💡 智能投资建议:')

                # 统计分析
                high_growth = [ind for ind in strong_industries[:10]
                             if performance_df[performance_df['industry'] == ind]['change_pct_1d'].iloc[0] > 3]
                high_volume = [ind for ind in strong_industries[:10]
                             if performance_df[performance_df['industry'] == ind].get('volume_ratio', pd.Series([1])).iloc[0] > 1.5]

                print(f'   🔥 高增长行业({len(high_growth)}个): 重点关注，适合短线操作')
                if high_growth:
                    print(f'      {", ".join(high_growth[:3])}{"等" if len(high_growth) > 3 else ""}')

                print(f'   💰 高成交行业({len(high_volume)}个): 资金关注度高，流动性好')
                if high_volume:
                    print(f'      {", ".join(high_volume[:3])}{"等" if len(high_volume) > 3 else ""}')

                print(f'   🎯 建议配置: 优先选择前{min(5, len(strong_industries))}个强势行业')
                print(f'   ⚠️ 风险提示: 强势行业波动较大，注意仓位控制')

            else:
                print('\n⚠️ 当前市场未发现明显强势行业')
                print('💡 建议: 可关注防御性行业或等待市场信号明确')

        else:
            print('❌ 未获取到行业数据')

    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

def test_industry_filtering():
    """测试行业过滤功能"""
    print('\n\n🔍 行业过滤功能测试')
    print('='*50)

    analyzer = IndustryAnalyzer()

    try:
        # 获取所有行业数据
        performance_df = analyzer.calculate_industry_performance()

        if not performance_df.empty:
            all_industries = performance_df['industry'].tolist()
            print(f'📊 总行业数: {len(all_industries)}')

            # 显示实际可用的行业名称
            print(f'📋 实际可用行业: {all_industries[:10]}...')

            # 模拟用户选择的行业 (使用实际存在的行业名称)
            available_industries = ['化学制药', '贵金属', '医疗服务', '汽车整车', '医疗器械']
            selected_industries = [ind for ind in available_industries if ind in all_industries]
            print(f'👤 用户选择行业: {selected_industries}')

            # 过滤数据
            filtered_df = performance_df[performance_df['industry'].isin(selected_industries)]
            print(f'✅ 过滤后行业数: {len(filtered_df)}')

            if not filtered_df.empty:
                print('\n📈 用户关注行业表现:')
                for i, (_, row) in enumerate(filtered_df.iterrows(), 1):
                    change_pct = row['change_pct_1d']
                    emoji = "🔥" if change_pct > 2 else "📈" if change_pct > 0 else "🔴"
                    print(f'  {i}. {emoji} {row["industry"]} ({change_pct:+.2f}%)')
            else:
                print('⚠️ 选择的行业中没有找到数据')

        else:
            print('❌ 未获取到行业数据')

    except Exception as e:
        print(f'❌ 测试失败: {e}')

def main():
    """主测试函数"""
    print('🚀 智能行业选择功能测试')
    print('='*60)

    # 测试智能推荐
    test_smart_industry_recommendation()

    # 测试行业过滤
    test_industry_filtering()

    print('\n🎉 智能行业选择功能测试完成！')
    print('\n✅ 新功能特点:')
    print('  🎯 实时分析强势行业并智能推荐')
    print('  💰 结合涨幅和成交量综合评估')
    print('  📊 提供详细的推荐理由和投资建议')
    print('  🔄 支持智能推荐和手动选择两种模式')
    print('  ⚙️ 分析时只关注用户选择的行业')

if __name__ == "__main__":
    main()
