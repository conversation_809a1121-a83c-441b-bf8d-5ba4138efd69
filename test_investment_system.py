"""
测试完整投资决策系统
"""

from fundamental_analyzer import FundamentalAnalyzer
from investment_advisor import InvestmentAdvisor
from deep_research import DeepResearchAnalyzer
from technical_analyzer import TechnicalAnalyzer
from industry_analyzer import IndustryAnalyzer

def test_fundamental_analysis():
    """测试基本面分析"""
    print('📊 基本面分析功能测试')
    print('='*50)
    
    analyzer = FundamentalAnalyzer()
    test_stocks = ['000001', '300750', '000858']  # 平安银行、宁德时代、五粮液
    
    for stock_code in test_stocks:
        print(f'\n🔍 分析股票: {stock_code}')
        
        try:
            # 获取公司基本信息
            company_info = analyzer.get_company_info(stock_code)
            if company_info:
                print(f'   ✅ 公司信息: {len(company_info)}项')
                company_name = company_info.get('股票简称', stock_code)
                industry = company_info.get('所属行业', '未知')
                print(f'   📈 {company_name} - {industry}')
            
            # 计算估值指标
            valuation = analyzer.calculate_valuation_metrics(stock_code)
            if valuation:
                print(f'   💰 当前价格: {valuation.get("current_price", 0):.2f}元')
                print(f'   📊 PE比率: {valuation.get("pe_ratio", 0):.2f} ({valuation.get("pe_rating", "未知")})')
                print(f'   📊 PB比率: {valuation.get("pb_ratio", 0):.2f} ({valuation.get("pb_rating", "未知")})')
                print(f'   🏢 市值: {valuation.get("market_cap_yi", 0):.0f}亿 ({valuation.get("market_cap_rating", "未知")})')
            
            # 成长潜力分析
            growth = analyzer.analyze_growth_potential(stock_code)
            if growth:
                print(f'   📈 成长评级: {growth.get("growth_rating", "未知")} (评分: {growth.get("growth_score", 0)}/100)')
            
            # 综合分析
            comprehensive = analyzer.comprehensive_analysis(stock_code)
            if comprehensive:
                print(f'   🎯 投资评级: {comprehensive.get("investment_rating", "中性")}')
                print(f'   ⚠️ 风险等级: {comprehensive.get("risk_level", "中等")}')
                print(f'   📊 综合评分: {comprehensive.get("total_score", 0):.1f}/100')
                
                recommendations = comprehensive.get('recommendation', [])
                if recommendations:
                    print(f'   💡 投资建议:')
                    for rec in recommendations[:2]:
                        print(f'     • {rec}')
            
        except Exception as e:
            print(f'   ❌ 分析失败: {e}')

def test_investment_advisor():
    """测试投资决策顾问"""
    print('\n\n🎯 投资决策顾问功能测试')
    print('='*50)
    
    # 初始化组件
    fundamental_analyzer = FundamentalAnalyzer()
    technical_analyzer = TechnicalAnalyzer()
    industry_analyzer = IndustryAnalyzer()
    advisor = InvestmentAdvisor()
    
    test_stock = '000001'  # 平安银行
    print(f'🔍 测试股票: {test_stock}')
    
    try:
        # 获取各项分析
        print('\n📊 获取基本面分析...')
        fundamental_analysis = fundamental_analyzer.comprehensive_analysis(test_stock)
        
        print('📈 获取技术面分析...')
        technical_analysis = technical_analyzer.analyze_stock_technical(test_stock)
        
        print('💰 获取资金流向分析...')
        fund_flow_analysis = industry_analyzer.fund_monitor.analyze_fund_flow_signals(test_stock)
        
        # 用户画像
        user_profiles = [
            {'risk_tolerance': 'conservative', 'portfolio_size': 100000, 'investment_horizon': '长期'},
            {'risk_tolerance': 'moderate', 'portfolio_size': 500000, 'investment_horizon': '中期'},
            {'risk_tolerance': 'aggressive', 'portfolio_size': 1000000, 'investment_horizon': '短期'}
        ]
        
        for i, user_profile in enumerate(user_profiles, 1):
            print(f'\n👤 用户画像 {i}: {user_profile["risk_tolerance"]} - {user_profile["portfolio_size"]/10000:.0f}万')
            
            # 生成投资决策
            decision = advisor.generate_investment_decision(
                fundamental_analysis, technical_analysis, fund_flow_analysis, user_profile
            )
            
            if decision:
                print(f'   🎯 投资决策: {decision.get("action", "hold")}')
                print(f'   📊 置信度: {decision.get("confidence", 0):.1f}%')
                print(f'   💰 建议仓位: {decision.get("position_percentage", "0%")}')
                print(f'   📈 目标价位: {decision.get("target_price", 0):.2f}元')
                print(f'   🛡️ 止损价位: {decision.get("stop_loss", 0):.2f}元')
                
                risk_assessment = decision.get('risk_assessment', {})
                print(f'   ⚠️ 风险等级: {risk_assessment.get("risk_level", "未知")}')
                
                detailed_advice = decision.get('detailed_advice', [])
                if detailed_advice:
                    print(f'   💡 详细建议:')
                    for advice in detailed_advice[:3]:
                        print(f'     • {advice}')
            else:
                print('   ❌ 决策生成失败')
                
    except Exception as e:
        print(f'❌ 投资决策测试失败: {e}')

def test_deep_research():
    """测试深入研究功能"""
    print('\n\n📊 深入研究功能测试')
    print('='*50)
    
    researcher = DeepResearchAnalyzer()
    test_stock = '000001'
    
    print(f'🔍 深入研究股票: {test_stock}')
    
    try:
        # 获取公司新闻
        print('\n📰 获取公司新闻...')
        news_list = researcher.get_company_news(test_stock, days=30)
        if news_list:
            print(f'   ✅ 获取新闻: {len(news_list)}条')
            for i, news in enumerate(news_list[:3], 1):
                sentiment = news.get('sentiment', '中性')
                title = news.get('title', '')[:50] + '...' if len(news.get('title', '')) > 50 else news.get('title', '')
                print(f'   {i}. [{sentiment}] {title}')
        else:
            print('   ⚠️ 未获取到新闻')
        
        # 获取分析师评级
        print('\n👨‍💼 获取分析师评级...')
        analyst_ratings = researcher.get_analyst_ratings(test_stock)
        if analyst_ratings:
            print(f'   ✅ 平均评级: {analyst_ratings.get("average_rating", "未知")}')
            target_price = analyst_ratings.get('target_price', 0)
            if target_price > 0:
                print(f'   📈 目标价: {target_price:.2f}元')
            
            rating_dist = analyst_ratings.get('rating_distribution', {})
            if rating_dist:
                print(f'   📊 评级分布: {len(rating_dist)}种评级')
        else:
            print('   ⚠️ 未获取到分析师评级')
        
        # 股东结构分析
        print('\n👥 股东结构分析...')
        shareholder_analysis = researcher.get_shareholder_analysis(test_stock)
        if shareholder_analysis:
            concentration = shareholder_analysis.get('ownership_concentration', 0)
            print(f'   ✅ 前五大股东持股: {concentration:.2f}%')
            
            major_shareholders = shareholder_analysis.get('major_shareholders', [])
            if major_shareholders:
                print(f'   📊 十大股东: {len(major_shareholders)}位')
        else:
            print('   ⚠️ 未获取到股东信息')
        
        # 风险因素分析
        print('\n⚠️ 风险因素分析...')
        risk_factors = researcher.get_risk_factors(test_stock)
        if risk_factors:
            overall_risk = risk_factors.get('overall_risk_level', '中等风险')
            print(f'   ✅ 整体风险: {overall_risk}')
            
            risk_categories = ['business_risks', 'financial_risks', 'market_risks', 'regulatory_risks']
            risk_names = ['经营风险', '财务风险', '市场风险', '监管风险']
            
            for category, name in zip(risk_categories, risk_names):
                risks = risk_factors.get(category, [])
                if risks:
                    print(f'   ⚠️ {name}: {len(risks)}项')
        else:
            print('   ⚠️ 风险分析失败')
        
        # 生成综合研究报告
        print('\n📋 生成综合研究报告...')
        report = researcher.comprehensive_research_report(test_stock)
        if report:
            print(f'   ✅ 报告生成成功')
            print(f'   📝 执行摘要: {report.get("executive_summary", "")[:100]}...')
            
            highlights = report.get('investment_highlights', [])
            concerns = report.get('key_concerns', [])
            print(f'   ✨ 投资亮点: {len(highlights)}个')
            print(f'   ⚠️ 关注点: {len(concerns)}个')
            
            conclusion = report.get('research_conclusion', '')
            print(f'   📝 研究结论: {conclusion[:100]}...')
        else:
            print('   ❌ 报告生成失败')
            
    except Exception as e:
        print(f'❌ 深入研究测试失败: {e}')

def main():
    """主测试函数"""
    print('🚀 完整投资决策系统功能测试')
    print('='*60)
    
    # 基本面分析测试
    test_fundamental_analysis()
    
    # 投资决策顾问测试
    test_investment_advisor()
    
    # 深入研究测试
    test_deep_research()
    
    print('\n🎉 投资决策系统测试完成！')
    print('\n✅ 系统功能:')
    print('  📊 基本面分析 - 估值、成长性、财务指标')
    print('  📈 技术面分析 - 技术指标、交易信号')
    print('  💰 资金流向分析 - 主力资金、大单监控')
    print('  🎯 投资决策支持 - 综合评分、仓位建议')
    print('  📊 深入研究报告 - 新闻、评级、风险分析')
    print('  👤 个性化建议 - 基于用户风险偏好')

if __name__ == "__main__":
    main()
