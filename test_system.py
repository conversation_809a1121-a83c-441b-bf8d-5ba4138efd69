"""
系统测试脚本 - 验证各模块功能
"""

import sys
import traceback
from datetime import datetime
import pandas as pd

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")

    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import seaborn as sns
        print("✅ 基础库导入成功")
    except ImportError as e:
        print(f"❌ 基础库导入失败: {e}")
        return False

    try:
        import akshare as ak
        print("✅ AKShare导入成功")
    except ImportError as e:
        print(f"⚠️ AKShare导入失败: {e}")
        print("   请运行: pip install akshare")

    try:
        import config
        print("✅ 配置文件导入成功")
    except ImportError as e:
        print(f"❌ 配置文件导入失败: {e}")
        return False

    try:
        from data_fetcher import DataFetcher
        from industry_analyzer import IndustryAnalyzer
        from technical_analyzer import TechnicalAnalyzer
        from backtest_engine import BacktestEngine
        print("✅ 自定义模块导入成功")
    except ImportError as e:
        print(f"❌ 自定义模块导入失败: {e}")
        return False

    return True

def test_data_fetcher():
    """测试数据获取模块"""
    print("\n📊 测试数据获取模块...")

    try:
        from data_fetcher import DataFetcher

        fetcher = DataFetcher()

        # 测试获取股票基本信息
        print("   测试获取股票基本信息...")
        stock_info = fetcher.get_stock_basic_info()
        if not stock_info.empty:
            print(f"   ✅ 获取到 {len(stock_info)} 只股票信息")
        else:
            print("   ⚠️ 未获取到股票基本信息")

        # 测试获取行业数据
        print("   测试获取行业数据...")
        industry_data = fetcher.get_industry_data()
        if industry_data:
            print(f"   ✅ 获取到 {len(industry_data)} 类行业数据")
        else:
            print("   ⚠️ 未获取到行业数据")

        # 测试获取单只股票数据
        print("   测试获取股票价格数据...")
        stock_data = fetcher.get_stock_price_data("000001")
        if not stock_data.empty:
            print(f"   ✅ 获取到 {len(stock_data)} 条价格数据")
        else:
            print("   ⚠️ 未获取到股票价格数据")

        return True

    except Exception as e:
        print(f"   ❌ 数据获取模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_industry_analyzer():
    """测试行业分析模块"""
    print("\n🔄 测试行业分析模块...")

    try:
        from industry_analyzer import IndustryAnalyzer

        analyzer = IndustryAnalyzer()

        # 测试计算行业表现
        print("   测试计算行业表现...")
        performance_df = analyzer.calculate_industry_performance()
        if not performance_df.empty:
            print(f"   ✅ 计算了 {len(performance_df)} 个行业的表现")
            print(f"   📊 前3名行业: {', '.join(performance_df['industry'].head(3).tolist())}")
        else:
            print("   ⚠️ 未能计算行业表现")

        # 测试轮动信号检测
        if not performance_df.empty:
            print("   测试轮动信号检测...")
            signals = analyzer.detect_rotation_signals(performance_df)
            if signals:
                print(f"   ✅ 检测到轮动信号")
                print(f"   🟢 强势行业: {len(signals.get('strong_industries', []))}个")
                print(f"   🔴 弱势行业: {len(signals.get('weak_industries', []))}个")
            else:
                print("   ⚠️ 未检测到轮动信号")

        return True

    except Exception as e:
        print(f"   ❌ 行业分析模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_technical_analyzer():
    """测试技术分析模块"""
    print("\n📈 测试技术分析模块...")

    try:
        from technical_analyzer import TechnicalAnalyzer

        analyzer = TechnicalAnalyzer()

        # 测试技术指标计算
        print("   测试技术指标计算...")

        # 创建测试数据
        test_data = pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])

        # 测试MA
        ma5 = analyzer.calculate_ma(test_data, 5)
        if not ma5.empty:
            print("   ✅ MA计算成功")

        # 测试RSI
        rsi = analyzer.calculate_rsi(test_data, 5)
        if not rsi.empty:
            print("   ✅ RSI计算成功")

        # 测试MACD
        macd = analyzer.calculate_macd(test_data)
        if macd and 'macd' in macd:
            print("   ✅ MACD计算成功")

        # 测试股票技术分析（如果数据可用）
        print("   测试股票技术分析...")
        analysis = analyzer.analyze_stock_technical("000001")
        if analysis:
            print(f"   ✅ 技术分析完成，当前价格: {analysis.get('current_price', 'N/A')}")
        else:
            print("   ⚠️ 股票技术分析失败（可能是数据获取问题）")

        return True

    except Exception as e:
        print(f"   ❌ 技术分析模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_backtest_engine():
    """测试回测引擎"""
    print("\n🔬 测试回测引擎...")

    try:
        from backtest_engine import BacktestEngine, ma_crossover_strategy

        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000)
        engine.add_strategy(ma_crossover_strategy, "测试策略")

        print("   ✅ 回测引擎初始化成功")

        # 测试策略函数
        test_daily_data = {
            '000001': {'Close': 10.5, 'High': 10.8, 'Low': 10.2, 'Volume': 1000000}
        }

        test_historical_data = {
            '000001': pd.DataFrame({
                'Close': [10.0, 10.1, 10.2, 10.3, 10.4, 10.5],
                'High': [10.2, 10.3, 10.4, 10.5, 10.6, 10.8],
                'Low': [9.8, 9.9, 10.0, 10.1, 10.2, 10.2],
                'Volume': [1000000] * 6
            })
        }

        signals = ma_crossover_strategy(test_daily_data, test_historical_data, {'short_period': 2, 'long_period': 3})
        if signals:
            print("   ✅ 策略信号生成成功")

        return True

    except Exception as e:
        print(f"   ❌ 回测引擎测试失败: {e}")
        traceback.print_exc()
        return False

def test_config():
    """测试配置文件"""
    print("\n⚙️ 测试配置文件...")

    try:
        import config

        # 检查关键配置
        if hasattr(config, 'INDUSTRY_MAPPING') and config.INDUSTRY_MAPPING:
            print(f"   ✅ 行业映射配置: {len(config.INDUSTRY_MAPPING)} 个行业")

        if hasattr(config, 'LEADING_STOCKS') and config.LEADING_STOCKS:
            print(f"   ✅ 龙头股票配置: {len(config.LEADING_STOCKS)} 个行业")

        if hasattr(config, 'TECHNICAL_INDICATORS') and config.TECHNICAL_INDICATORS:
            print(f"   ✅ 技术指标配置: {len(config.TECHNICAL_INDICATORS)} 个参数")

        if hasattr(config, 'RISK_CONTROL') and config.RISK_CONTROL:
            print(f"   ✅ 风险控制配置: {len(config.RISK_CONTROL)} 个参数")

        return True

    except Exception as e:
        print(f"   ❌ 配置文件测试失败: {e}")
        return False

def test_streamlit_app():
    """测试Streamlit应用"""
    print("\n🌐 测试Streamlit应用...")

    try:
        import streamlit as st
        print("   ✅ Streamlit导入成功")

        # 检查主应用文件
        with open('main_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'st.title' in content:
                print("   ✅ 主应用文件格式正确")
            else:
                print("   ⚠️ 主应用文件可能有问题")

        return True

    except ImportError:
        print("   ⚠️ Streamlit未安装，请运行: pip install streamlit")
        return False
    except Exception as e:
        print(f"   ❌ Streamlit应用测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始系统测试")
    print("=" * 50)

    tests = [
        ("模块导入", test_imports),
        ("配置文件", test_config),
        ("数据获取", test_data_fetcher),
        ("行业分析", test_industry_analyzer),
        ("技术分析", test_technical_analyzer),
        ("回测引擎", test_backtest_engine),
        ("Web应用", test_streamlit_app)
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))

    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<12} {status}")
        if result:
            passed += 1

    print(f"\n📊 测试统计: {passed}/{total} 通过 ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        print("\n🚀 启动建议:")
        print("   1. Web界面: streamlit run main_app.py")
        print("   2. 命令行: python run_analysis.py")
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")
        print("\n🔧 修复建议:")
        print("   1. 检查依赖安装: pip install -r requirements.txt")
        print("   2. 检查网络连接")
        print("   3. 查看具体错误信息")

if __name__ == "__main__":
    run_all_tests()
