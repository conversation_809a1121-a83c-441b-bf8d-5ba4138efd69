"""
A股行业轮动分析系统 - 功能完整性检查
"""

import sys
import traceback
from datetime import datetime, timedelta
import pandas as pd

def check_all_functions():
    """检查所有功能模块"""
    print("🔍 A股行业轮动分析系统 - 功能完整性检查")
    print("=" * 60)
    
    results = {}
    
    # 1. 检查行业轮动分析
    print("\n1️⃣ 检查行业轮动分析功能...")
    try:
        from industry_analyzer import IndustryAnalyzer
        analyzer = IndustryAnalyzer()
        
        # 测试行业表现计算
        performance_df = analyzer.calculate_industry_performance()
        if not performance_df.empty:
            print(f"   ✅ 行业表现分析: 成功分析{len(performance_df)}个行业")
            
            # 测试轮动信号检测
            signals = analyzer.detect_rotation_signals(performance_df)
            if signals:
                strong_count = len(signals.get('strong_industries', []))
                weak_count = len(signals.get('weak_industries', []))
                print(f"   ✅ 轮动信号检测: 强势{strong_count}个，弱势{weak_count}个")
                results['industry_analysis'] = True
            else:
                print("   ⚠️ 轮动信号检测: 未检测到信号")
                results['industry_analysis'] = False
        else:
            print("   ❌ 行业表现分析: 未获取到数据")
            results['industry_analysis'] = False
            
    except Exception as e:
        print(f"   ❌ 行业轮动分析失败: {e}")
        results['industry_analysis'] = False
    
    # 2. 检查技术指标分析
    print("\n2️⃣ 检查技术指标分析功能...")
    try:
        from technical_analyzer import TechnicalAnalyzer
        tech_analyzer = TechnicalAnalyzer()
        
        # 测试技术分析
        analysis = tech_analyzer.analyze_stock_technical('000001')
        if analysis and 'indicators' in analysis:
            indicators_count = len(analysis['indicators'])
            signals_count = len(analysis['signals'])
            print(f"   ✅ 技术指标计算: {indicators_count}个指标")
            print(f"   ✅ 交易信号生成: {signals_count}个信号")
            print(f"   ✅ 当前价格: {analysis.get('current_price', 'N/A')}元")
            results['technical_analysis'] = True
        else:
            print("   ❌ 技术指标分析: 未获取到分析结果")
            results['technical_analysis'] = False
            
    except Exception as e:
        print(f"   ❌ 技术指标分析失败: {e}")
        results['technical_analysis'] = False
    
    # 3. 检查回测引擎
    print("\n3️⃣ 检查策略回测功能...")
    try:
        from backtest_engine import BacktestEngine, ma_crossover_strategy
        
        engine = BacktestEngine(initial_capital=100000)
        engine.add_strategy(ma_crossover_strategy, "测试策略")
        
        # 运行简短回测
        results_bt = engine.run_backtest(
            symbols=['000001'],
            start_date='2024-12-01',
            end_date='2024-12-31',
            strategy_params={'short_period': 5, 'long_period': 20}
        )
        
        if results_bt:
            print(f"   ✅ 回测引擎: 成功运行")
            print(f"   ✅ 策略执行: {results_bt.get('strategy_name', 'Unknown')}")
            print(f"   ✅ 收益计算: {results_bt.get('total_return', 0):.2%}")
            print(f"   ✅ 交易记录: {results_bt.get('total_trades', 0)}笔")
            results['backtest_engine'] = True
        else:
            print("   ❌ 回测引擎: 未获取到回测结果")
            results['backtest_engine'] = False
            
    except Exception as e:
        print(f"   ❌ 策略回测失败: {e}")
        results['backtest_engine'] = False
    
    # 4. 检查数据获取
    print("\n4️⃣ 检查数据获取功能...")
    try:
        from data_fetcher import DataFetcher
        fetcher = DataFetcher()
        
        # 测试股票数据获取
        stock_data = fetcher.get_stock_price_data('000001')
        if not stock_data.empty:
            print(f"   ✅ 股票数据: 获取{len(stock_data)}条记录")
        else:
            print("   ⚠️ 股票数据: 未获取到数据")
        
        # 测试行业数据获取
        industry_data = fetcher.get_industry_data()
        if industry_data:
            print(f"   ✅ 行业数据: 获取{len(industry_data)}类数据")
        else:
            print("   ⚠️ 行业数据: 未获取到数据")
        
        # 测试行业成分股获取
        stocks = fetcher.get_industry_stocks('化学制药', top_n=5)
        if not stocks.empty:
            print(f"   ✅ 行业成分股: 获取{len(stocks)}只股票")
        else:
            print("   ⚠️ 行业成分股: 未获取到数据")
        
        results['data_fetcher'] = True
        
    except Exception as e:
        print(f"   ❌ 数据获取失败: {e}")
        results['data_fetcher'] = False
    
    # 5. 检查Web界面
    print("\n5️⃣ 检查Web界面功能...")
    try:
        import streamlit as st
        
        # 检查主应用文件
        with open('main_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'st.title' in content and 'industry_rotation_page' in content:
            print("   ✅ Web界面: 主应用文件完整")
            print("   ✅ 页面模块: 包含所有功能页面")
            results['web_interface'] = True
        else:
            print("   ⚠️ Web界面: 主应用文件可能不完整")
            results['web_interface'] = False
            
    except Exception as e:
        print(f"   ❌ Web界面检查失败: {e}")
        results['web_interface'] = False
    
    # 6. 检查完整分析流程
    print("\n6️⃣ 检查完整分析流程...")
    try:
        from industry_analyzer import IndustryAnalyzer
        analyzer = IndustryAnalyzer()
        
        # 生成完整报告
        report = analyzer.generate_rotation_report()
        if report:
            components = ['industry_performance', 'rotation_signals', 'leading_stocks', 'recommendations']
            available_components = [comp for comp in components if comp in report and report[comp]]
            
            print(f"   ✅ 完整报告: 包含{len(available_components)}/{len(components)}个组件")
            
            if 'leading_stocks' in report and report['leading_stocks']:
                total_stocks = sum(len(stocks) for stocks in report['leading_stocks'].values())
                print(f"   ✅ 强势行业股票: 获取{total_stocks}只推荐股票")
            
            if 'recommendations' in report and report['recommendations']:
                print(f"   ✅ 投资建议: 生成{len(report['recommendations'])}条建议")
            
            results['complete_analysis'] = True
        else:
            print("   ❌ 完整分析流程: 未生成报告")
            results['complete_analysis'] = False
            
    except Exception as e:
        print(f"   ❌ 完整分析流程失败: {e}")
        results['complete_analysis'] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 功能完整性检查结果")
    print("=" * 60)
    
    function_names = {
        'industry_analysis': '行业轮动分析',
        'technical_analysis': '技术指标分析', 
        'backtest_engine': '策略回测引擎',
        'data_fetcher': '数据获取模块',
        'web_interface': 'Web界面',
        'complete_analysis': '完整分析流程'
    }
    
    passed = 0
    total = len(results)
    
    for key, status in results.items():
        name = function_names.get(key, key)
        status_text = "✅ 正常" if status else "❌ 异常"
        print(f"{name:<12} {status_text}")
        if status:
            passed += 1
    
    print(f"\n📈 总体状态: {passed}/{total} 功能正常 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有功能正常！系统可以完整使用。")
        print("\n🚀 推荐使用方式:")
        print("   1. Web界面: streamlit run main_app.py")
        print("   2. 完整分析: python run_analysis.py")
        print("   3. 快速分析: python run_analysis.py quick")
    elif passed >= total * 0.8:
        print("⚠️ 大部分功能正常，系统基本可用。")
        print("🔧 建议检查异常功能并修复。")
    else:
        print("❌ 多个功能异常，建议全面检查系统。")
        print("🔧 请检查网络连接、依赖包安装等基础环境。")
    
    return results

if __name__ == "__main__":
    check_all_functions()
