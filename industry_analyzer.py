"""
行业轮动分析模块 - 股票数据分析系统
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
from data_fetcher import DataFetcher
from config import *

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class IndustryAnalyzer:
    """行业轮动分析类"""

    def __init__(self):
        self.data_fetcher = DataFetcher()
        self.industry_performance = {}
        self.rotation_signals = {}

    def calculate_industry_performance(self, period_days: int = 30) -> pd.DataFrame:
        """计算行业表现"""
        try:
            # 获取行业数据
            industry_data = self.data_fetcher.get_industry_data()

            if 'sw_industry' not in industry_data or industry_data['sw_industry'].empty:
                logger.warning("未获取到申万行业数据")
                return pd.DataFrame()

            sw_data = industry_data['sw_industry']

            # 计算行业涨跌幅
            performance_data = []
            for _, row in sw_data.iterrows():
                try:
                    industry_name = row['指数名称']
                    current_price = float(row['最新价'])
                    change_pct = float(row['涨跌幅'])

                    performance_data.append({
                        'industry': industry_name,
                        'current_price': current_price,
                        'change_pct_1d': change_pct,
                        'volume': row.get('成交量', 0),
                        'turnover': row.get('成交额', 0)
                    })
                except Exception as e:
                    logger.warning(f"处理行业 {row.get('指数名称', 'Unknown')} 数据失败: {e}")
                    continue

            performance_df = pd.DataFrame(performance_data)

            # 计算行业强度评分
            if not performance_df.empty:
                performance_df['strength_score'] = (
                    performance_df['change_pct_1d'] * 0.4 +
                    performance_df['volume'].rank(pct=True) * 0.3 +
                    performance_df['turnover'].rank(pct=True) * 0.3
                )

                # 排序
                performance_df = performance_df.sort_values('strength_score', ascending=False)

            logger.info(f"计算了 {len(performance_df)} 个行业的表现")
            return performance_df

        except Exception as e:
            logger.error(f"计算行业表现失败: {e}")
            return pd.DataFrame()

    def detect_rotation_signals(self, performance_df: pd.DataFrame) -> Dict[str, List[str]]:
        """检测行业轮动信号"""
        try:
            if performance_df.empty:
                return {}

            signals = {
                'strong_industries': [],  # 强势行业
                'weak_industries': [],   # 弱势行业
                'rotation_candidates': [], # 轮动候选
                'defensive_industries': [] # 防御性行业
            }

            # 计算分位数
            q75 = performance_df['strength_score'].quantile(0.75)
            q25 = performance_df['strength_score'].quantile(0.25)

            # 分类行业
            for _, row in performance_df.iterrows():
                industry = row['industry']
                score = row['strength_score']
                change_pct = row['change_pct_1d']

                if score >= q75:
                    signals['strong_industries'].append(industry)
                elif score <= q25:
                    signals['weak_industries'].append(industry)

                # 轮动候选：涨幅适中但强度评分高
                if 0 < change_pct < 3 and score > performance_df['strength_score'].median():
                    signals['rotation_candidates'].append(industry)

                # 防御性行业：跌幅小且相对稳定
                if -1 < change_pct < 1:
                    signals['defensive_industries'].append(industry)

            logger.info("行业轮动信号检测完成")
            return signals

        except Exception as e:
            logger.error(f"检测行业轮动信号失败: {e}")
            return {}

    def analyze_policy_impact(self, policy_keywords: List[str]) -> Dict[str, Dict]:
        """分析政策对行业的影响"""
        try:
            policy_impact = {}

            # 获取政策相关新闻
            news_list = self.data_fetcher.get_policy_news(policy_keywords)

            # 分析每个政策关键词的影响
            for keyword in policy_keywords:
                keyword_news = [news for news in news_list if news['keyword'] == keyword]

                if not keyword_news:
                    continue

                # 根据关键词映射到受影响的行业
                affected_industries = []
                for policy_type, keywords in POLICY_KEYWORDS.items():
                    if keyword in keywords:
                        # 映射到具体行业
                        if '新能源' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('新能源', []))
                        elif 'AI' in policy_type or '人工智能' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('人工智能', []))
                        elif '医药' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('医药生物', []))
                        elif '消费' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('消费', []))
                        elif '科技' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('科技', []))
                        elif '金融' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('金融', []))
                        elif '基建' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('基建', []))
                        elif '地产' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('地产', []))

                policy_impact[keyword] = {
                    'news_count': len(keyword_news),
                    'affected_industries': list(set(affected_industries)),
                    'latest_news': keyword_news[:3],  # 最新3条新闻
                    'impact_score': min(len(keyword_news) * 0.1, 1.0)  # 影响评分
                }

            logger.info(f"分析了 {len(policy_impact)} 个政策关键词的影响")
            return policy_impact

        except Exception as e:
            logger.error(f"分析政策影响失败: {e}")
            return {}

    def get_leading_stocks_by_industry(self, industry: str, top_n: int = 5) -> pd.DataFrame:
        """获取行业龙头股票"""
        try:
            # 获取配置中的龙头股票
            if industry in LEADING_STOCKS:
                stocks_data = self.data_fetcher.get_leading_stocks_data(industry)
                return stocks_data.head(top_n)
            else:
                logger.warning(f"未找到行业 {industry} 的龙头股票配置")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取行业 {industry} 龙头股票失败: {e}")
            return pd.DataFrame()

    def generate_rotation_report(self) -> Dict:
        """生成行业轮动分析报告"""
        try:
            report = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_overview': {},
                'industry_performance': {},
                'rotation_signals': {},
                'policy_impact': {},
                'leading_stocks': {},
                'recommendations': []
            }

            # 1. 计算行业表现
            performance_df = self.calculate_industry_performance()
            if not performance_df.empty:
                report['industry_performance'] = performance_df.to_dict('records')

                # 2. 检测轮动信号
                signals = self.detect_rotation_signals(performance_df)
                report['rotation_signals'] = signals

                # 3. 分析政策影响
                all_keywords = []
                for keywords in POLICY_KEYWORDS.values():
                    all_keywords.extend(keywords)
                policy_impact = self.analyze_policy_impact(all_keywords[:10])  # 限制关键词数量
                report['policy_impact'] = policy_impact

                # 4. 获取强势行业的龙头股票
                for industry in signals.get('strong_industries', [])[:3]:  # 前3个强势行业
                    # 映射到配置中的行业名称
                    mapped_industry = self._map_industry_name(industry)
                    if mapped_industry:
                        leading_stocks = self.get_leading_stocks_by_industry(mapped_industry)
                        if not leading_stocks.empty:
                            report['leading_stocks'][industry] = leading_stocks.to_dict('records')

                # 5. 生成投资建议
                recommendations = self._generate_recommendations(signals, policy_impact)
                report['recommendations'] = recommendations

            logger.info("行业轮动分析报告生成完成")
            return report

        except Exception as e:
            logger.error(f"生成行业轮动分析报告失败: {e}")
            return {}

    def _map_industry_name(self, industry_name: str) -> Optional[str]:
        """映射行业名称到配置中的标准名称"""
        mapping = {
            '新能源汽车': '新能源',
            '光伏设备': '新能源',
            '风电设备': '新能源',
            '电池': '新能源',
            '计算机应用': '人工智能',
            '计算机设备': '人工智能',
            '通信设备': '科技',
            '电子': '科技',
            '半导体': '科技',
            '医药生物': '医药生物',
            '食品饮料': '消费',
            '家用电器': '消费',
            '纺织服装': '消费',
            '银行': '金融',
            '非银金融': '金融',
            '房地产': '地产',
            '建筑材料': '基建',
            '建筑装饰': '基建',
            '机械设备': '基建'
        }

        for key, value in mapping.items():
            if key in industry_name:
                return value
        return None

    def _generate_recommendations(self, signals: Dict, policy_impact: Dict) -> List[str]:
        """生成投资建议"""
        recommendations = []

        # 基于强势行业的建议
        if signals.get('strong_industries'):
            recommendations.append(
                f"关注强势行业：{', '.join(signals['strong_industries'][:3])}，"
                f"这些行业表现出较强的资金流入和上涨动能。"
            )

        # 基于轮动候选的建议
        if signals.get('rotation_candidates'):
            recommendations.append(
                f"轮动机会：{', '.join(signals['rotation_candidates'][:3])}，"
                f"这些行业可能是下一轮轮动的受益者。"
            )

        # 基于政策影响的建议
        high_impact_policies = [k for k, v in policy_impact.items() if v.get('impact_score', 0) > 0.5]
        if high_impact_policies:
            recommendations.append(
                f"政策受益：关注受政策利好影响的行业，特别是与{', '.join(high_impact_policies[:2])}相关的板块。"
            )

        # 防御性建议
        if signals.get('defensive_industries'):
            recommendations.append(
                f"防御配置：在市场不确定性较高时，可考虑配置{', '.join(signals['defensive_industries'][:2])}等防御性行业。"
            )

        return recommendations

    def plot_industry_performance(self, performance_df: pd.DataFrame, save_path: str = None):
        """绘制行业表现图表"""
        try:
            if performance_df.empty:
                logger.warning("无数据可绘制")
                return

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

            # 1. 行业涨跌幅排行
            top_industries = performance_df.head(10)
            ax1.barh(range(len(top_industries)), top_industries['change_pct_1d'])
            ax1.set_yticks(range(len(top_industries)))
            ax1.set_yticklabels(top_industries['industry'])
            ax1.set_xlabel('涨跌幅 (%)')
            ax1.set_title('行业涨跌幅排行榜 (前10)')
            ax1.grid(True, alpha=0.3)

            # 2. 强度评分分布
            ax2.hist(performance_df['strength_score'], bins=20, alpha=0.7, color='skyblue')
            ax2.set_xlabel('强度评分')
            ax2.set_ylabel('行业数量')
            ax2.set_title('行业强度评分分布')
            ax2.grid(True, alpha=0.3)

            # 3. 成交量vs涨跌幅散点图
            scatter = ax3.scatter(performance_df['change_pct_1d'], performance_df['volume'],
                                alpha=0.6, c=performance_df['strength_score'], cmap='viridis')
            ax3.set_xlabel('涨跌幅 (%)')
            ax3.set_ylabel('成交量')
            ax3.set_title('涨跌幅 vs 成交量')
            ax3.grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=ax3, label='强度评分')

            # 4. 强势行业饼图
            strong_count = len(performance_df[performance_df['strength_score'] >= performance_df['strength_score'].quantile(0.75)])
            weak_count = len(performance_df[performance_df['strength_score'] <= performance_df['strength_score'].quantile(0.25)])
            neutral_count = len(performance_df) - strong_count - weak_count

            sizes = [strong_count, neutral_count, weak_count]
            labels = ['强势', '中性', '弱势']
            colors = ['green', 'yellow', 'red']
            ax4.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax4.set_title('行业强弱分布')

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"图表已保存到: {save_path}")

            plt.show()

        except Exception as e:
            logger.error(f"绘制行业表现图表失败: {e}")
