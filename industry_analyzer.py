"""
行业轮动分析模块 - 股票数据分析系统
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
from data_fetcher import DataFetcher
from fund_flow_monitor import FundFlowMonitor
from config import *

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class IndustryAnalyzer:
    """行业轮动分析类"""

    def __init__(self):
        self.data_fetcher = DataFetcher()
        self.fund_monitor = FundFlowMonitor()
        self.industry_performance = {}
        self.rotation_signals = {}

    def calculate_industry_performance(self, period_days: int = 30) -> pd.DataFrame:
        """计算行业表现"""
        try:
            # 获取行业数据
            industry_data = self.data_fetcher.get_industry_data()

            if 'sw_industry' not in industry_data or industry_data['sw_industry'].empty:
                logger.warning("未获取到申万行业数据")
                return pd.DataFrame()

            sw_data = industry_data['sw_industry']

            # 计算行业涨跌幅
            performance_data = []
            for _, row in sw_data.iterrows():
                try:
                    # 适配不同数据源的字段名称
                    industry_name = row.get('板块名称', row.get('指数名称', row.get('name', 'Unknown')))
                    current_price = float(row.get('最新价', row.get('现价', 0)))
                    change_pct = float(row.get('涨跌幅', row.get('涨幅', 0)))

                    # 处理百分号格式的涨跌幅
                    if isinstance(change_pct, str) and '%' in str(change_pct):
                        change_pct = float(str(change_pct).replace('%', ''))

                    performance_data.append({
                        'industry': industry_name,
                        'current_price': current_price,
                        'change_pct_1d': change_pct,
                        'volume': row.get('成交量', row.get('上涨家数', 0)),
                        'turnover': row.get('成交额', row.get('总市值', 0)),
                        'market_cap': row.get('总市值', 0),
                        'turnover_rate': row.get('换手率', 0),
                        'up_count': row.get('上涨家数', 0),
                        'down_count': row.get('下跌家数', 0)
                    })
                except Exception as e:
                    logger.warning(f"处理行业 {row.get('板块名称', row.get('指数名称', 'Unknown'))} 数据失败: {e}")
                    continue

            performance_df = pd.DataFrame(performance_data)

            # 计算行业强度评分
            if not performance_df.empty:
                # 确保数值字段为数值类型
                numeric_cols = ['change_pct_1d', 'turnover', 'market_cap', 'turnover_rate', 'up_count']
                for col in numeric_cols:
                    if col in performance_df.columns:
                        performance_df[col] = pd.to_numeric(performance_df[col], errors='coerce').fillna(0)

                # 计算强度评分（使用多个维度）
                performance_df['strength_score'] = (
                    performance_df['change_pct_1d'] * 0.3 +  # 涨跌幅权重
                    performance_df['turnover'].rank(pct=True) * 0.2 +  # 成交额权重
                    performance_df['market_cap'].rank(pct=True) * 0.2 +  # 市值权重
                    performance_df['turnover_rate'].rank(pct=True) * 0.15 +  # 换手率权重
                    (performance_df['up_count'] / (performance_df['up_count'] + performance_df['down_count'] + 1)).rank(pct=True) * 0.15  # 上涨股票比例权重
                )

                # 排序
                performance_df = performance_df.sort_values('strength_score', ascending=False)

            logger.info(f"计算了 {len(performance_df)} 个行业的表现")
            return performance_df

        except Exception as e:
            logger.error(f"计算行业表现失败: {e}")
            return pd.DataFrame()

    def detect_rotation_signals(self, performance_df: pd.DataFrame) -> Dict[str, List[str]]:
        """检测行业轮动信号"""
        try:
            if performance_df.empty:
                return {}

            signals = {
                'strong_industries': [],  # 强势行业
                'weak_industries': [],   # 弱势行业
                'rotation_candidates': [], # 轮动候选
                'defensive_industries': [] # 防御性行业
            }

            # 计算分位数
            q75 = performance_df['strength_score'].quantile(0.75)
            q25 = performance_df['strength_score'].quantile(0.25)

            # 分类行业
            for _, row in performance_df.iterrows():
                industry = row['industry']
                score = row['strength_score']
                change_pct = row['change_pct_1d']

                if score >= q75:
                    signals['strong_industries'].append(industry)
                elif score <= q25:
                    signals['weak_industries'].append(industry)

                # 轮动候选：涨幅适中但强度评分高
                if 0 < change_pct < 3 and score > performance_df['strength_score'].median():
                    signals['rotation_candidates'].append(industry)

                # 防御性行业：跌幅小且相对稳定
                if -1 < change_pct < 1:
                    signals['defensive_industries'].append(industry)

            logger.info("行业轮动信号检测完成")
            return signals

        except Exception as e:
            logger.error(f"检测行业轮动信号失败: {e}")
            return {}

    def analyze_policy_impact(self, policy_keywords: List[str]) -> Dict[str, Dict]:
        """分析政策对行业的影响"""
        try:
            policy_impact = {}

            # 获取政策相关新闻
            news_list = self.data_fetcher.get_policy_news(policy_keywords)

            # 分析每个政策关键词的影响
            for keyword in policy_keywords:
                keyword_news = [news for news in news_list if news['keyword'] == keyword]

                if not keyword_news:
                    continue

                # 根据关键词映射到受影响的行业
                affected_industries = []
                for policy_type, keywords in POLICY_KEYWORDS.items():
                    if keyword in keywords:
                        # 映射到具体行业
                        if '新能源' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('新能源', []))
                        elif 'AI' in policy_type or '人工智能' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('人工智能', []))
                        elif '医药' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('医药生物', []))
                        elif '消费' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('消费', []))
                        elif '科技' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('科技', []))
                        elif '金融' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('金融', []))
                        elif '基建' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('基建', []))
                        elif '地产' in policy_type:
                            affected_industries.extend(INDUSTRY_MAPPING.get('地产', []))

                policy_impact[keyword] = {
                    'news_count': len(keyword_news),
                    'affected_industries': list(set(affected_industries)),
                    'latest_news': keyword_news[:3],  # 最新3条新闻
                    'impact_score': min(len(keyword_news) * 0.1, 1.0)  # 影响评分
                }

            logger.info(f"分析了 {len(policy_impact)} 个政策关键词的影响")
            return policy_impact

        except Exception as e:
            logger.error(f"分析政策影响失败: {e}")
            return {}

    def get_leading_stocks_by_industry(self, industry: str, top_n: int = 5) -> pd.DataFrame:
        """获取行业龙头股票"""
        try:
            # 获取配置中的龙头股票
            if industry in LEADING_STOCKS:
                stocks_data = self.data_fetcher.get_leading_stocks_data(industry)
                return stocks_data.head(top_n)
            else:
                logger.warning(f"未找到行业 {industry} 的龙头股票配置")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取行业 {industry} 龙头股票失败: {e}")
            return pd.DataFrame()

    def generate_rotation_report(self) -> Dict:
        """生成行业轮动分析报告"""
        try:
            report = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_overview': {},
                'industry_performance': {},
                'rotation_signals': {},
                'policy_impact': {},
                'leading_stocks': {},
                'recommendations': []
            }

            # 1. 计算行业表现
            performance_df = self.calculate_industry_performance()
            if not performance_df.empty:
                report['industry_performance'] = performance_df.to_dict('records')

                # 2. 检测轮动信号
                signals = self.detect_rotation_signals(performance_df)
                report['rotation_signals'] = signals

                # 3. 分析政策影响
                all_keywords = []
                for keywords in POLICY_KEYWORDS.values():
                    all_keywords.extend(keywords)
                policy_impact = self.analyze_policy_impact(all_keywords[:10])  # 限制关键词数量
                report['policy_impact'] = policy_impact

                # 4. 获取强势行业的股票
                for industry in signals.get('strong_industries', [])[:5]:  # 前5个强势行业
                    industry_stocks = []

                    # 首先尝试获取实际行业成分股
                    try:
                        stocks_data = self.data_fetcher.get_industry_stocks(industry, top_n=8)
                        if not stocks_data.empty:
                            # 处理股票数据
                            for _, row in stocks_data.iterrows():
                                stock_info = {
                                    'name': row.get('名称', row.get('股票名称', 'Unknown')),
                                    'code': row.get('代码', row.get('股票代码', 'Unknown')),
                                    'price': row.get('最新价', row.get('现价', 0)),
                                    'change_pct': row.get('涨跌幅', row.get('涨幅', 0)),
                                    'volume': row.get('成交量', 0),
                                    'turnover': row.get('成交额', 0)
                                }
                                industry_stocks.append(stock_info)
                    except Exception as e:
                        logger.warning(f"获取行业 {industry} 成分股失败: {e}")

                    # 如果没有获取到成分股，尝试从配置的龙头股票中获取
                    if not industry_stocks:
                        mapped_industry = self._map_industry_name(industry)
                        if mapped_industry:
                            leading_stocks = self.get_leading_stocks_by_industry(mapped_industry)
                            if not leading_stocks.empty:
                                industry_stocks = leading_stocks.to_dict('records')

                    if industry_stocks:
                        report['leading_stocks'][industry] = industry_stocks

                # 5. 生成投资建议
                recommendations = self._generate_recommendations(signals, policy_impact)
                report['recommendations'] = recommendations

            logger.info("行业轮动分析报告生成完成")
            return report

        except Exception as e:
            logger.error(f"生成行业轮动分析报告失败: {e}")
            return {}

    def _map_industry_name(self, industry_name: str) -> Optional[str]:
        """映射行业名称到配置中的标准名称"""
        mapping = {
            # 新能源相关
            '新能源汽车': '新能源',
            '光伏设备': '新能源',
            '风电设备': '新能源',
            '电池': '新能源',
            '储能': '新能源',
            '氢能源': '新能源',

            # 医药生物相关
            '医药生物': '医药生物',
            '化学制药': '医药生物',
            '中药': '医药生物',
            '医疗器械': '医药生物',
            '医疗服务': '医药生物',
            '医药商业': '医药生物',
            '生物制品': '医药生物',

            # 科技相关
            '计算机应用': '人工智能',
            '计算机设备': '人工智能',
            '通信设备': '科技',
            '电子': '科技',
            '半导体': '科技',
            '软件开发': '人工智能',
            '人工智能': '人工智能',

            # 消费相关
            '食品饮料': '消费',
            '家用电器': '消费',
            '纺织服装': '消费',
            '商业贸易': '消费',
            '休闲服务': '消费',
            '汽车整车': '消费',

            # 金融相关
            '银行': '金融',
            '非银金融': '金融',
            '保险': '金融',
            '券商': '金融',

            # 基建相关
            '房地产': '地产',
            '建筑材料': '基建',
            '建筑装饰': '基建',
            '机械设备': '基建',
            '交通运输': '基建',
            '公用事业': '基建',

            # 资源相关
            '贵金属': '资源',
            '有色金属': '资源',
            '钢铁': '资源',
            '煤炭': '资源',
            '石油石化': '资源',
            '化学原料': '资源',
            '化学制品': '资源',
            '化纤行业': '资源'
        }

        # 精确匹配
        if industry_name in mapping:
            return mapping[industry_name]

        # 模糊匹配
        for key, value in mapping.items():
            if key in industry_name or industry_name in key:
                return value

        return None

    def _generate_recommendations(self, signals: Dict, policy_impact: Dict) -> List[str]:
        """生成投资建议"""
        recommendations = []

        # 基于强势行业的建议
        if signals.get('strong_industries'):
            recommendations.append(
                f"关注强势行业：{', '.join(signals['strong_industries'][:3])}，"
                f"这些行业表现出较强的资金流入和上涨动能。"
            )

        # 基于轮动候选的建议
        if signals.get('rotation_candidates'):
            recommendations.append(
                f"轮动机会：{', '.join(signals['rotation_candidates'][:3])}，"
                f"这些行业可能是下一轮轮动的受益者。"
            )

        # 基于政策影响的建议
        high_impact_policies = [k for k, v in policy_impact.items() if v.get('impact_score', 0) > 0.5]
        if high_impact_policies:
            recommendations.append(
                f"政策受益：关注受政策利好影响的行业，特别是与{', '.join(high_impact_policies[:2])}相关的板块。"
            )

        # 防御性建议
        if signals.get('defensive_industries'):
            recommendations.append(
                f"防御配置：在市场不确定性较高时，可考虑配置{', '.join(signals['defensive_industries'][:2])}等防御性行业。"
            )

        return recommendations

    def plot_industry_performance(self, performance_df: pd.DataFrame, save_path: str = None):
        """绘制行业表现图表"""
        try:
            if performance_df.empty:
                logger.warning("无数据可绘制")
                return

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

            # 1. 行业涨跌幅排行
            top_industries = performance_df.head(10)
            ax1.barh(range(len(top_industries)), top_industries['change_pct_1d'])
            ax1.set_yticks(range(len(top_industries)))
            ax1.set_yticklabels(top_industries['industry'])
            ax1.set_xlabel('涨跌幅 (%)')
            ax1.set_title('行业涨跌幅排行榜 (前10)')
            ax1.grid(True, alpha=0.3)

            # 2. 强度评分分布
            ax2.hist(performance_df['strength_score'], bins=20, alpha=0.7, color='skyblue')
            ax2.set_xlabel('强度评分')
            ax2.set_ylabel('行业数量')
            ax2.set_title('行业强度评分分布')
            ax2.grid(True, alpha=0.3)

            # 3. 成交量vs涨跌幅散点图
            scatter = ax3.scatter(performance_df['change_pct_1d'], performance_df['volume'],
                                alpha=0.6, c=performance_df['strength_score'], cmap='viridis')
            ax3.set_xlabel('涨跌幅 (%)')
            ax3.set_ylabel('成交量')
            ax3.set_title('涨跌幅 vs 成交量')
            ax3.grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=ax3, label='强度评分')

            # 4. 强势行业饼图
            strong_count = len(performance_df[performance_df['strength_score'] >= performance_df['strength_score'].quantile(0.75)])
            weak_count = len(performance_df[performance_df['strength_score'] <= performance_df['strength_score'].quantile(0.25)])
            neutral_count = len(performance_df) - strong_count - weak_count

            sizes = [strong_count, neutral_count, weak_count]
            labels = ['强势', '中性', '弱势']
            colors = ['green', 'yellow', 'red']
            ax4.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax4.set_title('行业强弱分布')

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"图表已保存到: {save_path}")

            plt.show()

        except Exception as e:
            logger.error(f"绘制行业表现图表失败: {e}")

    def get_smart_leading_stocks(self, industry_name: str, top_n: int = 5) -> pd.DataFrame:
        """
        智能筛选行业内的小市值龙头股票
        筛选标准：
        1. 小市值 (50-500亿) - 具备爆发潜力
        2. 资金流入活跃 (换手率高) - 受资金青睐
        3. 技术面强势 (涨幅好) - 趋势向上
        4. 成交量活跃 - 市场关注度高
        """
        try:
            # 获取行业成分股
            stocks_df = self.data_fetcher.get_industry_stocks(industry_name, top_n=50)

            if stocks_df.empty:
                logger.warning(f"未获取到行业 {industry_name} 的成分股数据")
                return pd.DataFrame()

            # 筛选条件
            filtered_stocks = []

            for _, stock in stocks_df.iterrows():
                try:
                    stock_code = stock.get('代码', '')
                    stock_name = stock.get('名称', '')
                    current_price = float(stock.get('最新价', 0))
                    change_pct = float(stock.get('涨跌幅', 0))
                    turnover_rate = float(stock.get('换手率', 0))
                    volume = float(stock.get('成交量', 0))
                    turnover_amount = float(stock.get('成交额', 0))
                    pe_ratio = float(stock.get('市盈率-动态', 0))

                    # 估算市值 (使用成交额和换手率)
                    if turnover_rate > 0:
                        estimated_market_cap = turnover_amount / (turnover_rate / 100)
                    else:
                        estimated_market_cap = 0

                    # 筛选条件：小市值龙头股票 (基于实际可获得的数据)
                    if (turnover_rate >= 1.0 and         # 换手率≥1% (资金活跃)
                        change_pct >= -8.0 and           # 跌幅不超过8%
                        volume > 0 and                   # 有成交量
                        current_price > 3.0 and          # 价格>3元
                        turnover_amount > 1e7 and        # 成交额>1000万 (活跃度)
                        estimated_market_cap > 0):       # 能估算出市值

                        # 计算综合评分 (满分100分)
                        score = 0

                        # 涨跌幅评分 (30%) - 短期表现
                        if change_pct > 5:
                            score += 30
                        elif change_pct > 2:
                            score += 20
                        elif change_pct > 0:
                            score += 10
                        elif change_pct > -2:
                            score += 5

                        # 换手率评分 (25%) - 资金关注度
                        if turnover_rate > 10:
                            score += 25
                        elif turnover_rate > 5:
                            score += 20
                        elif turnover_rate > 3:
                            score += 15
                        else:
                            score += 10

                        # 市值评分 (20%) - 偏好小市值 (基于估算市值)
                        if estimated_market_cap <= 50e8:
                            score += 20  # 50亿以下最优
                        elif estimated_market_cap <= 100e8:
                            score += 18  # 100亿以下很好
                        elif estimated_market_cap <= 200e8:
                            score += 15  # 200亿以下较好
                        elif estimated_market_cap <= 400e8:
                            score += 12  # 400亿以下一般
                        elif estimated_market_cap <= 600e8:
                            score += 8   # 600亿以下偏大
                        else:
                            score += 5   # 600亿以上较大

                        # 成交量评分 (15%) - 活跃度
                        if volume > 1e8:  # 成交量>1亿
                            score += 15
                        elif volume > 5e7:
                            score += 10
                        else:
                            score += 5

                        # 价格位置评分 (10%) - 避免高价股
                        if 10 <= current_price <= 50:
                            score += 10
                        elif 5 <= current_price <= 80:
                            score += 5

                        # 获取资金流向信号 (可选，避免过多API调用)
                        fund_signals = None
                        fund_score_bonus = 0

                        try:
                            # 只对高评分股票进行资金流向分析
                            if score >= 60:
                                fund_signals = self.fund_monitor.analyze_fund_flow_signals(stock_code)
                                if fund_signals and 'fund_flow_score' in fund_signals:
                                    fund_score = fund_signals['fund_flow_score']
                                    # 资金流向评分作为加分项 (最多+20分)
                                    fund_score_bonus = min(20, fund_score * 0.2)
                                    score += fund_score_bonus
                        except Exception as e:
                            # 资金流向分析失败不影响主流程
                            pass

                        filtered_stocks.append({
                            '代码': stock_code,
                            '名称': stock_name,
                            '最新价': current_price,
                            '涨跌幅': change_pct,
                            '换手率': turnover_rate,
                            '总市值': estimated_market_cap,
                            '成交量': volume,
                            '成交额': turnover_amount,
                            '综合评分': score,
                            '资金流向评分': fund_score_bonus,
                            '资金流向趋势': fund_signals.get('main_fund_trend', 'unknown') if fund_signals else 'unknown',
                            '市值等级': '小市值' if estimated_market_cap <= 100e8 else '中小市值',
                            '资金活跃度': '高' if turnover_rate > 5 else '中' if turnover_rate > 3 else '一般',
                            '爆发潜力': '高' if score >= 70 else '中' if score >= 50 else '一般'
                        })

                except (ValueError, TypeError) as e:
                    continue

            if filtered_stocks:
                # 按综合评分排序
                result_df = pd.DataFrame(filtered_stocks)
                result_df = result_df.sort_values('综合评分', ascending=False)

                logger.info(f"为行业 {industry_name} 筛选出 {len(result_df)} 只小市值龙头股票")
                return result_df.head(top_n)
            else:
                logger.warning(f"行业 {industry_name} 未找到符合条件的小市值龙头股票")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"筛选行业 {industry_name} 小市值龙头股票失败: {e}")
            return pd.DataFrame()

    def get_explosive_growth_stocks(self, strong_industries: list, top_n: int = 10) -> pd.DataFrame:
        """
        获取强势行业中最具爆发潜力的小市值股票
        """
        all_explosive_stocks = []

        for industry in strong_industries[:5]:  # 分析前5个强势行业
            smart_stocks = self.get_smart_leading_stocks(industry, top_n=8)

            if not smart_stocks.empty:
                # 添加行业信息
                smart_stocks['所属行业'] = industry
                all_explosive_stocks.append(smart_stocks)

        if all_explosive_stocks:
            # 合并所有行业的股票
            combined_df = pd.concat(all_explosive_stocks, ignore_index=True)

            # 按综合评分排序，返回最具爆发潜力的股票
            return combined_df.sort_values('综合评分', ascending=False).head(top_n)
        else:
            return pd.DataFrame()

    def monitor_sudden_fund_inflow(self, stock_codes: list = None) -> dict:
        """
        监测突然的资金流入
        """
        try:
            if stock_codes is None:
                # 获取热门资金流入股票
                hot_stocks = self.fund_monitor.get_hot_money_stocks(top_n=50)
                if not hot_stocks.empty:
                    stock_codes = hot_stocks['代码'].tolist()
                else:
                    return {}

            # 监测突然资金变化
            sudden_changes = self.fund_monitor.monitor_sudden_fund_changes(stock_codes)

            # 分析结果
            analysis = {
                'timestamp': sudden_changes['timestamp'],
                'summary': {
                    'sudden_inflow_count': len(sudden_changes['sudden_inflow']),
                    'sudden_outflow_count': len(sudden_changes['sudden_outflow']),
                    'abnormal_activity_count': len(sudden_changes['abnormal_activity'])
                },
                'alerts': [],
                'recommendations': []
            }

            # 生成预警
            if sudden_changes['sudden_inflow']:
                top_inflow = sorted(sudden_changes['sudden_inflow'],
                                  key=lambda x: x['score'], reverse=True)[:5]
                analysis['alerts'].append({
                    'type': 'sudden_inflow',
                    'message': f"检测到{len(sudden_changes['sudden_inflow'])}只股票突然大量资金流入",
                    'stocks': top_inflow
                })

                # 生成投资建议
                for stock in top_inflow[:3]:
                    analysis['recommendations'].append(
                        f"🔥 {stock['stock_code']}: 资金大量流入，建议关注 (评分: {stock['score']}/100)"
                    )

            if sudden_changes['abnormal_activity']:
                analysis['alerts'].append({
                    'type': 'abnormal_activity',
                    'message': f"检测到{len(sudden_changes['abnormal_activity'])}只股票交易异常活跃",
                    'stocks': sudden_changes['abnormal_activity'][:5]
                })

            return analysis

        except Exception as e:
            logger.error(f"监测突然资金流入失败: {e}")
            return {}

    def get_fund_flow_enhanced_stocks(self, strong_industries: list, top_n: int = 10) -> pd.DataFrame:
        """
        获取结合资金流向分析的强势股票
        """
        try:
            # 获取基础的爆发潜力股票
            explosive_stocks = self.get_explosive_growth_stocks(strong_industries, top_n=top_n*2)

            if explosive_stocks.empty:
                return pd.DataFrame()

            # 对每只股票进行资金流向分析
            enhanced_stocks = []

            for _, stock in explosive_stocks.iterrows():
                stock_code = stock['代码']

                try:
                    # 获取资金流向信号
                    fund_signals = self.fund_monitor.analyze_fund_flow_signals(stock_code)

                    if fund_signals and 'fund_flow_score' in fund_signals:
                        # 创建增强版股票信息
                        enhanced_stock = stock.to_dict()
                        enhanced_stock.update({
                            '资金流向评分': fund_signals['fund_flow_score'],
                            '主力资金趋势': fund_signals['main_fund_trend'],
                            '大单活跃度': fund_signals['big_deal_activity'],
                            '资金流向信号': '; '.join(fund_signals['fund_flow_signals'][:3]),
                            '风险等级': fund_signals['risk_level']
                        })

                        # 重新计算综合评分 (加入资金流向权重)
                        original_score = enhanced_stock['综合评分']
                        fund_score = fund_signals['fund_flow_score']

                        # 综合评分 = 原评分*0.7 + 资金流向评分*0.3
                        final_score = original_score * 0.7 + fund_score * 0.3
                        enhanced_stock['最终评分'] = final_score

                        enhanced_stocks.append(enhanced_stock)

                except Exception as e:
                    # 资金流向分析失败，保留原始数据
                    enhanced_stock = stock.to_dict()
                    enhanced_stock.update({
                        '资金流向评分': 0,
                        '主力资金趋势': 'unknown',
                        '大单活跃度': 'unknown',
                        '资金流向信号': '数据获取失败',
                        '风险等级': 'unknown',
                        '最终评分': enhanced_stock['综合评分']
                    })
                    enhanced_stocks.append(enhanced_stock)

            if enhanced_stocks:
                result_df = pd.DataFrame(enhanced_stocks)
                # 按最终评分排序
                result_df = result_df.sort_values('最终评分', ascending=False)
                return result_df.head(top_n)
            else:
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取资金流向增强股票失败: {e}")
            return pd.DataFrame()
