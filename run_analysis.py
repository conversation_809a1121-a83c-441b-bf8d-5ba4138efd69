"""
快速分析脚本 - 股票数据分析系统
用于快速运行行业轮动分析和获取龙头股票信息
"""

import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import logging
from industry_analyzer import IndustryAnalyzer
from technical_analyzer import TechnicalAnalyzer
from config import *

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主分析函数"""
    print("🚀 启动A股行业轮动分析系统")
    print("=" * 50)

    # 初始化分析器
    industry_analyzer = IndustryAnalyzer()
    technical_analyzer = TechnicalAnalyzer()

    try:
        # 1. 行业轮动分析
        print("\n📊 正在进行行业轮动分析...")
        report = industry_analyzer.generate_rotation_report()

        if report:
            print("✅ 行业轮动分析完成！")

            # 显示分析结果
            print_analysis_results(report)

            # 绘制行业表现图表
            if 'industry_performance' in report and report['industry_performance']:
                performance_df = pd.DataFrame(report['industry_performance'])
                industry_analyzer.plot_industry_performance(
                    performance_df,
                    save_path='industry_performance.png'
                )

        # 2. 龙头股票技术分析
        print("\n📈 正在分析龙头股票技术指标...")
        analyze_leading_stocks(technical_analyzer, report)

        print("\n🎉 分析完成！请查看生成的图表和报告。")

    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")
        print(f"❌ 分析失败: {e}")

def print_analysis_results(report):
    """打印分析结果"""
    print("\n" + "="*50)
    print("📋 行业轮动分析报告")
    print("="*50)

    # 行业表现
    if 'industry_performance' in report and report['industry_performance']:
        print("\n📊 行业表现排行 (前10名):")
        performance_df = pd.DataFrame(report['industry_performance'])
        top_10 = performance_df.head(10)

        for i, row in top_10.iterrows():
            print(f"{i+1:2d}. {row['industry']:<20} {row['change_pct_1d']:>6.2f}%")

    # 轮动信号
    if 'rotation_signals' in report and report['rotation_signals']:
        signals = report['rotation_signals']
        print(f"\n🎯 轮动信号:")
        print(f"   强势行业 ({len(signals.get('strong_industries', []))}个): {', '.join(signals.get('strong_industries', [])[:5])}")
        print(f"   弱势行业 ({len(signals.get('weak_industries', []))}个): {', '.join(signals.get('weak_industries', [])[:5])}")
        print(f"   轮动候选 ({len(signals.get('rotation_candidates', []))}个): {', '.join(signals.get('rotation_candidates', [])[:5])}")

    # 投资建议
    if 'recommendations' in report and report['recommendations']:
        print(f"\n💡 投资建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"   {i}. {rec}")

    # 强势行业股票
    if 'leading_stocks' in report and report['leading_stocks']:
        print(f"\n🏆 强势行业股票:")
        for industry, stocks in report['leading_stocks'].items():
            print(f"\n   📈 {industry}:")
            for stock in stocks[:5]:  # 显示前5只
                name = stock.get('name', 'N/A')
                code = stock.get('code', 'N/A')
                price = stock.get('price', 0)
                change_pct = stock.get('change_pct', 0)

                # 格式化显示
                if isinstance(price, (int, float)) and price > 0:
                    price_str = f"{price:.2f}元"
                else:
                    price_str = "N/A"

                if isinstance(change_pct, (int, float)):
                    change_str = f"{change_pct:+.2f}%"
                    emoji = "🟢" if change_pct > 0 else "🔴" if change_pct < 0 else "⚪"
                else:
                    change_str = "N/A"
                    emoji = "⚪"

                print(f"     • {name} ({code}) {price_str} {emoji}{change_str}")

def analyze_leading_stocks(technical_analyzer, report):
    """分析龙头股票技术指标"""
    if not report or 'leading_stocks' not in report:
        print("   ⚠️ 未找到龙头股票数据")
        return

    print("\n📈 龙头股票技术分析:")

    # 分析每个行业的龙头股票
    for industry, stocks in report['leading_stocks'].items():
        print(f"\n   {industry} 行业:")

        for stock in stocks[:2]:  # 分析前2只龙头股
            stock_code = stock.get('code')
            stock_name = stock.get('name')

            if stock_code:
                try:
                    analysis = technical_analyzer.analyze_stock_technical(stock_code)

                    if analysis:
                        current_price = analysis.get('current_price', 0)
                        overall_signal = analysis.get('signals', {}).get('overall_signal', 'neutral')
                        rsi = analysis.get('indicators', {}).get('RSI', 0)

                        signal_emoji = "🟢" if overall_signal == 'bullish' else "🔴" if overall_signal == 'bearish' else "🟡"

                        print(f"     • {stock_name} ({stock_code}): {current_price:.2f}元 {signal_emoji}{overall_signal} RSI:{rsi:.1f}")
                    else:
                        print(f"     • {stock_name} ({stock_code}): 数据获取失败")

                except Exception as e:
                    print(f"     • {stock_name} ({stock_code}): 分析失败 - {e}")

def quick_industry_analysis():
    """快速行业分析"""
    print("🔍 快速行业分析模式")

    analyzer = IndustryAnalyzer()

    # 计算行业表现
    performance_df = analyzer.calculate_industry_performance()

    if not performance_df.empty:
        print("\n📊 当前行业表现:")
        print(performance_df[['industry', 'change_pct_1d', 'strength_score']].head(10).to_string(index=False))

        # 检测轮动信号
        signals = analyzer.detect_rotation_signals(performance_df)

        print(f"\n🎯 轮动信号:")
        for signal_type, industries in signals.items():
            if industries:
                print(f"   {signal_type}: {', '.join(industries[:3])}")
    else:
        print("❌ 无法获取行业数据")

def analyze_specific_stocks(stock_codes):
    """分析指定股票"""
    print(f"📈 分析指定股票: {', '.join(stock_codes)}")

    analyzer = TechnicalAnalyzer()

    for stock_code in stock_codes:
        try:
            print(f"\n分析股票: {stock_code}")
            analysis = analyzer.analyze_stock_technical(stock_code)

            if analysis:
                print(f"  当前价格: {analysis['current_price']:.2f}元")
                print(f"  综合信号: {analysis['signals']['overall_signal']}")
                print(f"  RSI: {analysis['indicators'].get('RSI', 0):.1f}")
                print(f"  MACD: {analysis['indicators'].get('MACD', 0):.4f}")
            else:
                print(f"  ❌ 无法获取数据")

        except Exception as e:
            print(f"  ❌ 分析失败: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            quick_industry_analysis()
        elif sys.argv[1] == "stocks":
            if len(sys.argv) > 2:
                stock_codes = sys.argv[2].split(',')
                analyze_specific_stocks(stock_codes)
            else:
                print("请提供股票代码，例如: python run_analysis.py stocks 000001,600519")
        else:
            print("使用方法:")
            print("  python run_analysis.py          # 完整分析")
            print("  python run_analysis.py quick    # 快速行业分析")
            print("  python run_analysis.py stocks 000001,600519  # 分析指定股票")
    else:
        main()
