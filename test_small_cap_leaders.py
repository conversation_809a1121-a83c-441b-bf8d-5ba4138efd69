"""
测试小市值龙头股票筛选功能
"""

from industry_analyzer import IndustryAnalyzer

def test_small_cap_leaders():
    """测试小市值龙头股票筛选"""
    print('🚀 小市值龙头股票筛选功能测试')
    print('='*60)
    
    analyzer = IndustryAnalyzer()
    
    # 获取强势行业
    performance_df = analyzer.calculate_industry_performance()
    signals = analyzer.detect_rotation_signals(performance_df)
    strong_industries = signals.get('strong_industries', [])
    
    print(f'📈 当前强势行业: {len(strong_industries)}个')
    for i, industry in enumerate(strong_industries[:5], 1):
        industry_data = performance_df[performance_df['industry'] == industry]
        if not industry_data.empty:
            change_pct = industry_data.iloc[0]['change_pct_1d']
            print(f'  {i}. {industry} ({change_pct:+.2f}%)')
    
    print('\n🎯 小市值龙头股票筛选测试:')
    print('='*60)
    
    # 测试单个行业的小市值龙头股票
    test_industry = strong_industries[0] if strong_industries else '化学制药'
    print(f'\n📊 测试行业: {test_industry}')
    
    smart_stocks = analyzer.get_smart_leading_stocks(test_industry, top_n=8)
    
    if not smart_stocks.empty:
        print(f'✅ 成功筛选出 {len(smart_stocks)} 只小市值龙头股票')
        print('\n🏆 筛选结果:')
        print('-' * 80)
        
        for i, (_, stock) in enumerate(smart_stocks.iterrows(), 1):
            market_cap_yi = stock['总市值'] / 1e8
            
            # 根据评分设置标识
            if stock['综合评分'] >= 70:
                emoji = "🔥"
            elif stock['综合评分'] >= 60:
                emoji = "⭐"
            else:
                emoji = "💎"
            
            print(f'{i:2d}. {emoji} {stock["名称"]} ({stock["代码"]})')
            print(f'     价格: {stock["最新价"]:.2f}元 | 涨跌: {stock["涨跌幅"]:+.2f}% | 换手: {stock["换手率"]:.2f}%')
            print(f'     市值: {market_cap_yi:.0f}亿 | 评分: {stock["综合评分"]:.0f}/100 | 潜力: {stock["爆发潜力"]}')
            print()
        
        # 统计分析
        print('📊 筛选统计:')
        print('-' * 40)
        
        high_potential = smart_stocks[smart_stocks['爆发潜力'] == '高']
        small_cap = smart_stocks[smart_stocks['市值等级'] == '小市值']
        high_active = smart_stocks[smart_stocks['资金活跃度'] == '高']
        rising = smart_stocks[smart_stocks['涨跌幅'] > 0]
        
        print(f'🔥 高潜力股票: {len(high_potential)}只 ({len(high_potential)/len(smart_stocks)*100:.0f}%)')
        print(f'💎 小市值股票: {len(small_cap)}只 ({len(small_cap)/len(smart_stocks)*100:.0f}%)')
        print(f'🌊 资金活跃股票: {len(high_active)}只 ({len(high_active)/len(smart_stocks)*100:.0f}%)')
        print(f'📈 上涨股票: {len(rising)}只 ({len(rising)/len(smart_stocks)*100:.0f}%)')
        
        # 平均指标
        avg_score = smart_stocks['综合评分'].mean()
        avg_turnover = smart_stocks['换手率'].mean()
        avg_market_cap = smart_stocks['总市值'].mean() / 1e8
        
        print(f'\n📈 平均指标:')
        print(f'  • 平均评分: {avg_score:.1f}/100')
        print(f'  • 平均换手率: {avg_turnover:.2f}%')
        print(f'  • 平均市值: {avg_market_cap:.0f}亿')
        
    else:
        print(f'⚠️ 行业 {test_industry} 未找到符合条件的小市值龙头股票')
    
    print('\n🚀 爆发潜力股票综合筛选测试:')
    print('='*60)
    
    # 测试爆发潜力股票筛选
    explosive_stocks = analyzer.get_explosive_growth_stocks(strong_industries, top_n=10)
    
    if not explosive_stocks.empty:
        print(f'✅ 成功筛选出 {len(explosive_stocks)} 只最具爆发潜力的股票')
        print('\n🏆 爆发潜力排行榜:')
        print('-' * 80)
        
        for i, (_, stock) in enumerate(explosive_stocks.iterrows(), 1):
            market_cap_yi = stock['总市值'] / 1e8
            
            if stock['综合评分'] >= 70:
                emoji = "🔥"
            elif stock['综合评分'] >= 60:
                emoji = "⭐"
            else:
                emoji = "💎"
            
            print(f'{i:2d}. {emoji} {stock["名称"]} ({stock["代码"]})')
            print(f'     行业: {stock["所属行业"]} | 价格: {stock["最新价"]:.2f}元 | 涨跌: {stock["涨跌幅"]:+.2f}%')
            print(f'     市值: {market_cap_yi:.0f}亿 | 换手: {stock["换手率"]:.2f}% | 评分: {stock["综合评分"]:.0f}/100')
            print()
        
        # 投资建议
        print('💡 投资建议:')
        print('-' * 40)
        
        top_3 = explosive_stocks.head(3)
        rising_stocks = explosive_stocks[explosive_stocks['涨跌幅'] > 0]
        high_turnover = explosive_stocks[explosive_stocks['换手率'] > 5]
        
        if not top_3.empty:
            top_names = ', '.join(top_3['名称'].tolist())
            print(f'🎯 重点关注: {top_names}')
        
        if not rising_stocks.empty:
            print(f'📈 上涨趋势: {len(rising_stocks)}只股票当日上涨')
        
        if not high_turnover.empty:
            print(f'🌊 资金青睐: {len(high_turnover)}只股票换手率>5%')
        
        print('🚀 操作建议: 小市值股票波动大，建议分批建仓')
        print('⚠️ 风险控制: 单只仓位≤5%，总体小市值仓位≤30%')
        
    else:
        print('⚠️ 当前未找到符合条件的爆发潜力股票')
    
    print('\n🎉 小市值龙头股票筛选功能测试完成！')
    print('✅ 功能特点:')
    print('  • 智能筛选50-500亿市值股票')
    print('  • 多维度评分算法 (涨跌幅+换手率+市值+成交量+价格)')
    print('  • 资金活跃度分析')
    print('  • 爆发潜力评估')
    print('  • 综合排行榜')

if __name__ == "__main__":
    test_small_cap_leaders()
