"""
测试资金流向监测功能
"""

from fund_flow_monitor import FundFlowMonitor
from industry_analyzer import IndustryAnalyzer

def test_fund_flow_monitor():
    """测试资金流向监测功能"""
    print('💰 资金流向监测功能测试')
    print('='*60)
    
    monitor = FundFlowMonitor()
    
    # 测试个股资金流向
    print('\n1. 个股资金流向测试:')
    print('-' * 40)
    
    test_stocks = ['000001', '300750', '002594']  # 平安银行、宁德时代、比亚迪
    
    for stock_code in test_stocks:
        try:
            print(f'\n📊 分析股票: {stock_code}')
            
            # 获取资金流向数据
            fund_flow = monitor.get_stock_fund_flow(stock_code, days=3)
            if not fund_flow.empty:
                print(f'   ✅ 获取资金流向数据: {len(fund_flow)}条')
                latest = fund_flow.iloc[-1]
                print(f'   💰 最新主力净流入: {latest.get("主力净流入", 0)/1e8:.2f}亿')
                print(f'   📊 主力净流入占比: {latest.get("主力净流入占比", 0):.2f}%')
            else:
                print(f'   ⚠️ 未获取到资金流向数据')
            
            # 分析资金流向信号
            signals = monitor.analyze_fund_flow_signals(stock_code)
            if signals and 'fund_flow_score' in signals:
                print(f'   🎯 资金流向评分: {signals["fund_flow_score"]}/100')
                print(f'   📈 主力资金趋势: {signals["main_fund_trend"]}')
                print(f'   ⚡ 大单活跃度: {signals["big_deal_activity"]}')
                print(f'   ⚠️ 风险等级: {signals["risk_level"]}')
                
                if signals['fund_flow_signals']:
                    print(f'   💡 资金流向信号:')
                    for signal in signals['fund_flow_signals'][:3]:
                        print(f'     • {signal}')
            else:
                print(f'   ❌ 资金流向信号分析失败')
                
        except Exception as e:
            print(f'   ❌ 股票{stock_code}分析失败: {e}')
    
    # 测试行业资金流向
    print('\n\n2. 行业资金流向测试:')
    print('-' * 40)
    
    try:
        industry_flow = monitor.get_industry_fund_flow()
        if not industry_flow.empty:
            print(f'✅ 获取行业资金流向: {len(industry_flow)}个行业')
            
            print('\n💰 资金流入前5行业:')
            top_inflow = industry_flow.nlargest(5, '主力净流入')
            for i, (_, row) in enumerate(top_inflow.iterrows(), 1):
                name = row['名称']
                inflow = row['主力净流入'] / 1e8
                print(f'  {i}. {name}: {inflow:+.2f}亿')
            
            print('\n📉 资金流出前5行业:')
            top_outflow = industry_flow.nsmallest(5, '主力净流入')
            for i, (_, row) in enumerate(top_outflow.iterrows(), 1):
                name = row['名称']
                outflow = row['主力净流入'] / 1e8
                print(f'  {i}. {name}: {outflow:+.2f}亿')
        else:
            print('⚠️ 未获取到行业资金流向数据')
    except Exception as e:
        print(f'❌ 行业资金流向测试失败: {e}')
    
    # 测试热门资金流入股票
    print('\n\n3. 热门资金流入股票测试:')
    print('-' * 40)
    
    try:
        hot_stocks = monitor.get_hot_money_stocks(top_n=10)
        if not hot_stocks.empty:
            print(f'✅ 获取热门资金流入股票: {len(hot_stocks)}只')
            
            print('\n🔥 资金流入前10股票:')
            for i, (_, stock) in enumerate(hot_stocks.iterrows(), 1):
                name = stock['名称']
                code = stock['代码']
                inflow = stock['主力净流入'] / 1e8
                inflow_pct = stock.get('主力净流入占比', 0)
                print(f'  {i:2d}. {name}({code}): {inflow:+.2f}亿 ({inflow_pct:+.1f}%)')
        else:
            print('⚠️ 未获取到热门资金流入股票')
    except Exception as e:
        print(f'❌ 热门资金流入股票测试失败: {e}')

def test_integrated_analysis():
    """测试集成的资金流向分析"""
    print('\n\n🚀 集成资金流向分析测试')
    print('='*60)
    
    analyzer = IndustryAnalyzer()
    
    # 测试突然资金流入监测
    print('\n1. 突然资金流入监测:')
    print('-' * 40)
    
    try:
        sudden_analysis = analyzer.monitor_sudden_fund_inflow()
        
        if sudden_analysis:
            summary = sudden_analysis.get('summary', {})
            print(f'✅ 监测完成')
            print(f'   💰 突然流入股票: {summary.get("sudden_inflow_count", 0)}只')
            print(f'   📉 突然流出股票: {summary.get("sudden_outflow_count", 0)}只')
            print(f'   ⚡ 异常活跃股票: {summary.get("abnormal_activity_count", 0)}只')
            
            # 显示预警
            alerts = sudden_analysis.get('alerts', [])
            for alert in alerts:
                print(f'\n🚨 预警: {alert["message"]}')
                if alert.get('stocks'):
                    for stock in alert['stocks'][:3]:
                        print(f'   • {stock["stock_code"]} (评分: {stock["score"]}/100)')
            
            # 显示建议
            recommendations = sudden_analysis.get('recommendations', [])
            if recommendations:
                print(f'\n💡 投资建议:')
                for rec in recommendations:
                    print(f'   • {rec}')
        else:
            print('⚠️ 未检测到突然资金变化')
    except Exception as e:
        print(f'❌ 突然资金流入监测失败: {e}')
    
    # 测试资金流向增强股票筛选
    print('\n\n2. 资金流向增强股票筛选:')
    print('-' * 40)
    
    try:
        # 获取强势行业
        performance_df = analyzer.calculate_industry_performance()
        signals = analyzer.detect_rotation_signals(performance_df)
        strong_industries = signals.get('strong_industries', [])
        
        if strong_industries:
            print(f'📈 强势行业: {len(strong_industries)}个')
            
            # 获取资金流向增强股票
            enhanced_stocks = analyzer.get_fund_flow_enhanced_stocks(strong_industries[:3], top_n=5)
            
            if not enhanced_stocks.empty:
                print(f'✅ 获取资金流向增强股票: {len(enhanced_stocks)}只')
                
                print('\n🏆 资金流向增强排行榜:')
                for i, (_, stock) in enumerate(enhanced_stocks.iterrows(), 1):
                    name = stock['名称']
                    code = stock['代码']
                    final_score = stock.get('最终评分', stock.get('综合评分', 0))
                    fund_score = stock.get('资金流向评分', 0)
                    fund_trend = stock.get('主力资金趋势', 'unknown')
                    
                    print(f'  {i}. {name}({code})')
                    print(f'     最终评分: {final_score:.1f}/100 | 资金评分: {fund_score:.1f}/100')
                    print(f'     资金趋势: {fund_trend} | 行业: {stock["所属行业"]}')
                    print()
            else:
                print('⚠️ 未获取到资金流向增强股票')
        else:
            print('⚠️ 未找到强势行业')
    except Exception as e:
        print(f'❌ 资金流向增强股票筛选失败: {e}')

def main():
    """主测试函数"""
    print('🎯 开始资金流向监测功能全面测试')
    
    # 基础功能测试
    test_fund_flow_monitor()
    
    # 集成功能测试
    test_integrated_analysis()
    
    print('\n🎉 资金流向监测功能测试完成！')
    print('\n✅ 功能特点:')
    print('  • 个股资金流向分析')
    print('  • 行业资金流向排行')
    print('  • 热门资金流入股票')
    print('  • 突然资金变化监测')
    print('  • 资金流向增强评分')
    print('  • 实时大单监控')
    print('  • 综合投资建议')

if __name__ == "__main__":
    main()
