#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量控制修复验证脚本
测试修复后的严格数据质量控制
"""

import pandas as pd
from limit_up_page import validate_stock_data_integrity, analyze_single_stock_thread_safe

def test_quality_control_fix():
    """测试数据质量控制修复效果"""
    print("🔧 数据质量控制修复验证")
    print("=" * 50)
    print()
    
    # 创建测试数据 - 模拟你看到的异常情况
    test_cases = [
        {
            'name': '严重异常数据 (9000%差异)',
            'data': {
                '代码': '600530',
                '名称': '交通银行',
                '最新价': 7.58,
                '昨收': 7.30,
                '涨跌幅': 3.84,
                '成交量': 50000000,
                '成交额': 37637500000,  # 导致成交均价752.75元
                '换手率': 2.5
            }
        },
        {
            'name': '中等异常数据 (100%差异)',
            'data': {
                '代码': '000001',
                '名称': '平安银行',
                '最新价': 12.50,
                '昨收': 12.00,
                '涨跌幅': 4.17,
                '成交量': 30000000,
                '成交额': 750000000,  # 导致成交均价25元 (100%差异)
                '换手率': 2.0
            }
        },
        {
            'name': '轻微异常数据 (30%差异)',
            'data': {
                '代码': '000002',
                '名称': '万科A',
                '最新价': 15.00,
                '昨收': 14.50,
                '涨跌幅': 3.45,
                '成交量': 40000000,
                '成交额': 780000000,  # 导致成交均价19.5元 (30%差异)
                '换手率': 1.8
            }
        },
        {
            'name': '正常数据',
            'data': {
                '代码': '600519',
                '名称': '贵州茅台',
                '最新价': 1800.00,
                '昨收': 1750.00,
                '涨跌幅': 2.86,
                '成交量': 1000000,
                '成交额': 1810000000,  # 导致成交均价1810元 (0.6%差异)
                '换手率': 0.5
            }
        }
    ]
    
    print("📊 测试各种数据质量情况:")
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. {test_case['name']}")
        print("-" * 40)
        
        data = test_case['data']
        
        # 计算实际差异
        volume = data['成交量']
        amount = data['成交额']
        current_price = data['最新价']
        avg_price = amount / volume
        diff_ratio = abs(avg_price - current_price) / current_price * 100
        
        print(f"   成交均价: {avg_price:.2f}元")
        print(f"   当前价格: {current_price:.2f}元")
        print(f"   差异比例: {diff_ratio:.1f}%")
        
        # 测试数据验证
        validation = validate_stock_data_integrity(data)
        print(f"   验证结果: {'✅ 通过' if validation['is_valid'] else '❌ 失败'}")
        print(f"   质量评分: {validation['data_quality_score']:.0f}分")
        
        if validation['errors']:
            print("   ❌ 错误:")
            for error in validation['errors']:
                print(f"      • {error}")
        
        if validation['warnings']:
            print("   ⚠️ 警告:")
            for warning in validation['warnings']:
                print(f"      • {warning}")
        
        # 测试分析函数
        print("   分析测试:", end=" ")
        try:
            result = analyze_single_stock_thread_safe(pd.Series(data), enable_fund_analysis=False)
            if result:
                print("✅ 进入分析")
            else:
                print("❌ 拒绝分析")
        except Exception as e:
            print(f"❌ 分析失败: {e}")
        
        print()
    
    print("🎯 修复效果验证:")
    print()
    
    # 统计结果
    validation_results = []
    analysis_results = []
    
    for test_case in test_cases:
        data = test_case['data']
        validation = validate_stock_data_integrity(data)
        validation_results.append(validation['is_valid'])
        
        try:
            result = analyze_single_stock_thread_safe(pd.Series(data), enable_fund_analysis=False)
            analysis_results.append(result is not None)
        except:
            analysis_results.append(False)
    
    print("验证通过情况:")
    for i, (test_case, passed) in enumerate(zip(test_cases, validation_results)):
        status = "✅ 通过" if passed else "❌ 拒绝"
        print(f"   {test_case['name']}: {status}")
    
    print()
    print("分析执行情况:")
    for i, (test_case, analyzed) in enumerate(zip(test_cases, analysis_results)):
        status = "✅ 执行" if analyzed else "❌ 拒绝"
        print(f"   {test_case['name']}: {status}")
    
    print()
    print("🔍 关键验证点:")
    
    # 检查严重异常数据是否被正确拒绝
    severe_case_rejected = not validation_results[0] and not analysis_results[0]
    print(f"   严重异常数据被拒绝: {'✅ 是' if severe_case_rejected else '❌ 否'}")
    
    # 检查正常数据是否通过
    normal_case_passed = validation_results[3] and analysis_results[3]
    print(f"   正常数据正常通过: {'✅ 是' if normal_case_passed else '❌ 否'}")
    
    # 检查中等异常数据处理
    medium_case_status = validation_results[1]
    print(f"   中等异常数据处理: {'✅ 正确拒绝' if not medium_case_status else '⚠️ 仍然通过'}")
    
    print()
    if severe_case_rejected and normal_case_passed:
        print("🎉 修复成功! 数据质量控制正常工作!")
        print("   • 严重异常数据被正确拒绝")
        print("   • 正常数据正常通过验证")
        print("   • 系统现在只分析高质量数据")
    else:
        print("⚠️ 修复可能不完全，需要进一步调整")
    
    print()
    print("💡 总结:")
    print("   现在系统会严格控制数据质量")
    print("   只有质量评分≥80分且无严重异常的数据才会被分析")
    print("   这确保了分析结果的可靠性和准确性")

if __name__ == "__main__":
    test_quality_control_fix()
