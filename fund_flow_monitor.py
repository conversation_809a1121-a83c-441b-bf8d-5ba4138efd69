"""
资金流向实时监测模块
"""

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class FundFlowMonitor:
    """资金流向监测器"""
    
    def __init__(self):
        self.cache_timeout = 300  # 5分钟缓存
        self.cache = {}
    
    def get_stock_fund_flow(self, stock_code: str, days: int = 5) -> pd.DataFrame:
        """
        获取个股资金流向数据
        """
        try:
            # 获取个股资金流向
            fund_flow = ak.stock_individual_fund_flow(stock=stock_code, market='sz' if stock_code.startswith('0') else 'sh')
            
            if not fund_flow.empty:
                # 获取最近几天的数据
                fund_flow = fund_flow.tail(days)
                logger.info(f"获取股票 {stock_code} 资金流向数据: {len(fund_flow)} 条")
                return fund_flow
            else:
                logger.warning(f"未获取到股票 {stock_code} 的资金流向数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 资金流向失败: {e}")
            return pd.DataFrame()
    
    def get_industry_fund_flow(self) -> pd.DataFrame:
        """
        获取行业资金流向排行
        """
        try:
            # 获取今日行业资金流向
            industry_flow = ak.stock_sector_fund_flow_rank(indicator='今日', sector_type='行业资金流')
            
            if not industry_flow.empty:
                logger.info(f"获取行业资金流向数据: {len(industry_flow)} 个行业")
                return industry_flow
            else:
                logger.warning("未获取到行业资金流向数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取行业资金流向失败: {e}")
            return pd.DataFrame()
    
    def get_real_time_big_deals(self, stock_code: str) -> pd.DataFrame:
        """
        获取实时大单数据
        """
        try:
            # 构造股票代码
            if stock_code.startswith('0') or stock_code.startswith('3'):
                symbol = f'sz{stock_code}'
            else:
                symbol = f'sh{stock_code}'
            
            # 获取实时大单数据
            big_deals = ak.stock_zh_a_tick_tx_js(symbol=symbol)
            
            if not big_deals.empty:
                logger.info(f"获取股票 {stock_code} 实时大单数据: {len(big_deals)} 条")
                return big_deals
            else:
                logger.warning(f"未获取到股票 {stock_code} 的实时大单数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 实时大单数据失败: {e}")
            return pd.DataFrame()
    
    def analyze_fund_flow_signals(self, stock_code: str) -> dict:
        """
        分析资金流向信号
        """
        try:
            signals = {
                'stock_code': stock_code,
                'fund_flow_score': 0,
                'main_fund_trend': 'neutral',
                'big_deal_activity': 'normal',
                'fund_flow_signals': [],
                'risk_level': 'medium'
            }
            
            # 1. 获取资金流向数据
            fund_flow = self.get_stock_fund_flow(stock_code, days=5)
            
            if not fund_flow.empty:
                # 分析主力资金流向
                latest_flow = fund_flow.iloc[-1]
                main_inflow = float(latest_flow.get('主力净流入', 0))
                main_inflow_pct = float(latest_flow.get('主力净流入占比', 0))
                
                # 计算资金流向评分
                score = 0
                
                # 主力净流入评分 (40%)
                if main_inflow > 1e8:  # 超过1亿
                    score += 40
                    signals['fund_flow_signals'].append(f"大额主力流入: {main_inflow/1e8:.2f}亿")
                elif main_inflow > 5e7:  # 超过5000万
                    score += 30
                    signals['fund_flow_signals'].append(f"主力流入: {main_inflow/1e8:.2f}亿")
                elif main_inflow > 0:
                    score += 20
                elif main_inflow > -5e7:
                    score += 10
                else:
                    signals['fund_flow_signals'].append(f"主力流出: {abs(main_inflow)/1e8:.2f}亿")
                
                # 主力净流入占比评分 (30%)
                if main_inflow_pct > 10:
                    score += 30
                    signals['fund_flow_signals'].append(f"主力占比极高: {main_inflow_pct:.1f}%")
                elif main_inflow_pct > 5:
                    score += 20
                    signals['fund_flow_signals'].append(f"主力占比较高: {main_inflow_pct:.1f}%")
                elif main_inflow_pct > 0:
                    score += 10
                
                # 连续流入评分 (30%)
                if len(fund_flow) >= 3:
                    recent_flows = fund_flow.tail(3)['主力净流入'].astype(float)
                    positive_days = (recent_flows > 0).sum()
                    
                    if positive_days == 3:
                        score += 30
                        signals['fund_flow_signals'].append("连续3日主力流入")
                    elif positive_days == 2:
                        score += 20
                        signals['fund_flow_signals'].append("近期主力持续流入")
                    elif positive_days == 1:
                        score += 10
                
                signals['fund_flow_score'] = score
                
                # 判断主力资金趋势
                if main_inflow > 5e7 and main_inflow_pct > 5:
                    signals['main_fund_trend'] = 'strong_inflow'
                elif main_inflow > 0:
                    signals['main_fund_trend'] = 'inflow'
                elif main_inflow < -5e7:
                    signals['main_fund_trend'] = 'outflow'
                else:
                    signals['main_fund_trend'] = 'neutral'
            
            # 2. 分析大单活跃度
            big_deals = self.get_real_time_big_deals(stock_code)
            
            if not big_deals.empty:
                # 统计大单数量和金额
                big_deal_count = len(big_deals)
                
                if big_deal_count > 100:
                    signals['big_deal_activity'] = 'very_active'
                    signals['fund_flow_signals'].append(f"大单极度活跃: {big_deal_count}笔")
                elif big_deal_count > 50:
                    signals['big_deal_activity'] = 'active'
                    signals['fund_flow_signals'].append(f"大单活跃: {big_deal_count}笔")
                elif big_deal_count > 20:
                    signals['big_deal_activity'] = 'normal'
                else:
                    signals['big_deal_activity'] = 'quiet'
            
            # 3. 综合风险评估
            if signals['fund_flow_score'] >= 70:
                signals['risk_level'] = 'low'  # 资金大量流入，风险较低
            elif signals['fund_flow_score'] >= 50:
                signals['risk_level'] = 'medium'
            else:
                signals['risk_level'] = 'high'  # 资金流出，风险较高
            
            return signals
            
        except Exception as e:
            logger.error(f"分析股票 {stock_code} 资金流向信号失败: {e}")
            return {'stock_code': stock_code, 'error': str(e)}
    
    def get_hot_money_stocks(self, top_n: int = 20) -> pd.DataFrame:
        """
        获取热门资金流入股票
        """
        try:
            # 获取今日资金流向排行
            fund_rank = ak.stock_individual_fund_flow_rank(indicator='今日')
            
            if not fund_rank.empty:
                # 筛选主力净流入为正的股票
                hot_stocks = fund_rank[fund_rank['主力净流入'] > 0].head(top_n)
                logger.info(f"获取热门资金流入股票: {len(hot_stocks)} 只")
                return hot_stocks
            else:
                logger.warning("未获取到资金流向排行数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取热门资金流入股票失败: {e}")
            return pd.DataFrame()
    
    def monitor_sudden_fund_changes(self, stock_codes: list) -> dict:
        """
        监测突然的资金变化
        """
        sudden_changes = {
            'timestamp': datetime.now(),
            'sudden_inflow': [],
            'sudden_outflow': [],
            'abnormal_activity': []
        }
        
        for stock_code in stock_codes:
            try:
                signals = self.analyze_fund_flow_signals(stock_code)
                
                # 检测突然流入
                if signals.get('main_fund_trend') == 'strong_inflow':
                    sudden_changes['sudden_inflow'].append({
                        'stock_code': stock_code,
                        'score': signals.get('fund_flow_score', 0),
                        'signals': signals.get('fund_flow_signals', [])
                    })
                
                # 检测突然流出
                elif signals.get('main_fund_trend') == 'outflow':
                    sudden_changes['sudden_outflow'].append({
                        'stock_code': stock_code,
                        'score': signals.get('fund_flow_score', 0),
                        'signals': signals.get('fund_flow_signals', [])
                    })
                
                # 检测异常活跃
                if signals.get('big_deal_activity') in ['very_active', 'active']:
                    sudden_changes['abnormal_activity'].append({
                        'stock_code': stock_code,
                        'activity': signals.get('big_deal_activity'),
                        'signals': signals.get('fund_flow_signals', [])
                    })
                    
            except Exception as e:
                logger.error(f"监测股票 {stock_code} 资金变化失败: {e}")
                continue
        
        return sudden_changes
