"""
测试封板预测功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from limit_up_predictor import LimitUpPredictor
import pandas as pd

def test_limit_up_prediction():
    """测试封板预测功能"""
    print("🚀 封板预测功能测试")
    print("=" * 60)
    
    # 初始化预测器
    predictor = LimitUpPredictor()
    
    print("📊 正在扫描潜在涨停股票...")
    print("-" * 40)
    
    try:
        # 扫描潜在涨停股票
        potential_stocks = predictor.scan_potential_limit_up_stocks(top_n=15)
        
        if not potential_stocks.empty:
            print(f"✅ 成功分析 {len(potential_stocks)} 只潜在涨停股票")
            print("\n🏆 封板概率排行榜:")
            print("-" * 80)
            
            for i, (_, stock) in enumerate(potential_stocks.iterrows(), 1):
                probability = stock['limit_up_probability']
                risk_level = stock['risk_level']
                current_price = stock['current_price']
                current_change = stock['current_change_pct']
                distance = stock['distance_to_limit_up']
                
                # 根据概率设置emoji
                if probability >= 80:
                    emoji = "🔥🔥"
                elif probability >= 65:
                    emoji = "🔥"
                elif probability >= 50:
                    emoji = "⭐"
                elif probability >= 35:
                    emoji = "📈"
                else:
                    emoji = "💎"
                
                print(f"{i:2d}. {emoji} {stock['stock_name']} ({stock['stock_code']})")
                print(f"     封板概率: {probability:.1f}% | 风险等级: {risk_level}")
                print(f"     当前价格: {current_price:.2f}元 | 当前涨幅: {current_change:+.2f}%")
                print(f"     距离涨停: {distance:.2f}% | 行业: {stock.get('industry', '未知')}")
                print(f"     建议: {stock['recommendation']}")
                print()
            
            # 统计分析
            print("📊 封板预测统计:")
            print("-" * 40)
            
            high_prob = potential_stocks[potential_stocks['limit_up_probability'] >= 65]
            medium_prob = potential_stocks[
                (potential_stocks['limit_up_probability'] >= 50) & 
                (potential_stocks['limit_up_probability'] < 65)
            ]
            rising_stocks = potential_stocks[potential_stocks['current_change_pct'] > 0]
            close_to_limit = potential_stocks[potential_stocks['distance_to_limit_up'] < 3]
            
            print(f"🔥 高概率封板: {len(high_prob)}只 ({len(high_prob)/len(potential_stocks)*100:.0f}%)")
            print(f"⭐ 中等概率封板: {len(medium_prob)}只 ({len(medium_prob)/len(potential_stocks)*100:.0f}%)")
            print(f"📈 当日上涨: {len(rising_stocks)}只 ({len(rising_stocks)/len(potential_stocks)*100:.0f}%)")
            print(f"🎯 接近涨停: {len(close_to_limit)}只 ({len(close_to_limit)/len(potential_stocks)*100:.0f}%)")
            
            # 平均指标
            avg_probability = potential_stocks['limit_up_probability'].mean()
            avg_change = potential_stocks['current_change_pct'].mean()
            avg_distance = potential_stocks['distance_to_limit_up'].mean()
            
            print(f"\n📈 平均指标:")
            print(f"  • 平均封板概率: {avg_probability:.1f}%")
            print(f"  • 平均当前涨幅: {avg_change:.2f}%")
            print(f"  • 平均距离涨停: {avg_distance:.2f}%")
            
            # 投资建议
            print("\n💡 封板预测投资建议:")
            print("-" * 40)
            
            if not high_prob.empty:
                top_3_high = high_prob.head(3)
                top_names = ', '.join(top_3_high['stock_name'].tolist())
                print(f"🎯 重点关注: {top_names}")
                print(f"   理由: 封板概率≥65%，具备较强封板潜力")
            
            if not close_to_limit.empty:
                close_count = len(close_to_limit)
                print(f"⚡ 临界关注: {close_count}只股票距离涨停<3%")
                print(f"   策略: 密切监控，一旦突破可能快速封板")
            
            if not rising_stocks.empty:
                rising_count = len(rising_stocks)
                print(f"📈 趋势向好: {rising_count}只股票当日上涨")
                print(f"   信号: 市场情绪积极，有利于封板")
            
            print("\n🚀 操作建议:")
            print("  • 高概率股票: 可适当关注，但需控制仓位")
            print("  • 中等概率股票: 观望为主，等待更强信号")
            print("  • 风险控制: 涨停板风险极高，严格止损")
            print("  • 时机把握: 开盘30分钟内是关键观察期")
            
            print("\n⚠️ 风险提示:")
            print("  • 涨停预测仅供参考，不构成投资建议")
            print("  • 市场变化快速，预测可能失效")
            print("  • 严格控制单只股票仓位≤2%")
            print("  • 设置止损位，及时止盈止损")
            
        else:
            print("⚠️ 当前市场条件下未找到符合条件的潜在涨停股票")
            print("💡 建议:")
            print("  • 市场可能处于调整期")
            print("  • 等待更好的市场时机")
            print("  • 关注政策面和资金面变化")
        
    except Exception as e:
        print(f"❌ 封板预测分析失败: {e}")
        print("💡 可能原因:")
        print("  • 网络连接问题")
        print("  • 数据源暂时不可用")
        print("  • 非交易时间")
    
    print("\n🎉 封板预测功能测试完成！")
    print("✅ 功能特点:")
    print("  • 基于昨日数据和今日开盘情况")
    print("  • 多维度评分算法 (昨日表现+今日开盘+资金流向)")
    print("  • 主板股票专门筛选")
    print("  • 封板概率量化评估")
    print("  • 风险等级智能分类")

def test_single_stock_prediction():
    """测试单只股票封板预测"""
    print("\n" + "=" * 60)
    print("🎯 单只股票封板预测测试")
    print("=" * 60)
    
    predictor = LimitUpPredictor()
    
    # 测试几只主板股票
    test_stocks = ['000001', '600519', '000002', '600036', '000858']
    
    for stock_code in test_stocks:
        try:
            print(f"\n📊 分析股票: {stock_code}")
            print("-" * 30)
            
            prediction = predictor.calculate_limit_up_probability(stock_code)
            
            if 'error' not in prediction:
                probability = prediction['limit_up_probability']
                risk_level = prediction['risk_level']
                current_price = prediction['current_price']
                current_change = prediction['current_change_pct']
                distance = prediction['distance_to_limit_up']
                
                print(f"股票名称: {prediction.get('stock_name', '未知')}")
                print(f"封板概率: {probability:.1f}%")
                print(f"风险等级: {risk_level}")
                print(f"当前价格: {current_price:.2f}元")
                print(f"当前涨幅: {current_change:+.2f}%")
                print(f"距离涨停: {distance:.2f}%")
                print(f"投资建议: {prediction['recommendation']}")
                
                # 详细评分
                yesterday_score = prediction['yesterday_score']
                today_score = prediction['today_score']
                fund_score = prediction['fund_score']
                
                print(f"评分详情: 昨日{yesterday_score:.0f} + 今日{today_score:.0f} + 资金{fund_score:.0f} = {prediction['total_score']:.0f}")
            else:
                print(f"❌ 分析失败: {prediction['error']}")
                
        except Exception as e:
            print(f"❌ 股票 {stock_code} 分析异常: {e}")

if __name__ == "__main__":
    test_limit_up_prediction()
    test_single_stock_prediction()
