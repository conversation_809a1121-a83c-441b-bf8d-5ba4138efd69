"""
回测引擎模块 - 股票数据分析系统
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Callable
from data_fetcher import DataFetcher
from technical_analyzer import TechnicalAnalyzer
from config import *

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class BacktestEngine:
    """回测引擎类"""

    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.data_fetcher = DataFetcher()
        self.technical_analyzer = TechnicalAnalyzer()
        self.reset()

    def reset(self):
        """重置回测状态"""
        self.capital = self.initial_capital
        self.positions = {}  # {symbol: shares}
        self.trades = []
        self.portfolio_values = []
        self.returns = []
        self.max_drawdown = 0
        self.current_drawdown = 0
        self.peak_value = self.initial_capital

    def add_strategy(self, strategy_func: Callable, name: str = "Custom Strategy"):
        """添加交易策略"""
        self.strategy_func = strategy_func
        self.strategy_name = name

    def run_backtest(self, symbols: List[str], start_date: str, end_date: str,
                    strategy_params: Dict = None) -> Dict:
        """运行回测"""
        try:
            self.reset()

            if strategy_params is None:
                strategy_params = {}

            # 转换日期格式
            start_date_pd = pd.to_datetime(start_date)
            end_date_pd = pd.to_datetime(end_date)

            # 获取所有股票数据
            stock_data = {}
            for symbol in symbols:
                data = self.data_fetcher.get_stock_price_data(symbol)
                if not data.empty:
                    # 确保索引是datetime类型
                    if not isinstance(data.index, pd.DatetimeIndex):
                        data.index = pd.to_datetime(data.index)

                    # 过滤日期范围
                    data = data[(data.index >= start_date_pd) & (data.index <= end_date_pd)]
                    if not data.empty:
                        stock_data[symbol] = data

            if not stock_data:
                logger.warning("未获取到任何股票数据")
                return {}

            # 获取交易日期
            all_dates = set()
            for data in stock_data.values():
                all_dates.update(data.index)
            trading_dates = sorted(list(all_dates))

            # 逐日回测
            for date in trading_dates:
                try:
                    # 获取当日数据
                    daily_data = {}
                    for symbol, data in stock_data.items():
                        if date in data.index:
                            daily_data[symbol] = data.loc[date]

                    if not daily_data:
                        continue

                    # 执行策略
                    if hasattr(self, 'strategy_func'):
                        signals = self.strategy_func(daily_data, stock_data, strategy_params)
                        self._execute_trades(signals, daily_data, date)

                    # 计算组合价值
                    portfolio_value = self._calculate_portfolio_value(daily_data)
                    self.portfolio_values.append({
                        'date': date,
                        'value': portfolio_value,
                        'capital': self.capital,
                        'positions': self.positions.copy()
                    })

                    # 计算回撤
                    self._update_drawdown(portfolio_value)

                except Exception as e:
                    logger.warning(f"处理日期 {date} 数据失败: {e}")
                    continue

            # 计算回测结果
            if trading_dates:
                results = self._calculate_backtest_results(trading_dates[0], trading_dates[-1])
                logger.info(f"回测完成，策略: {getattr(self, 'strategy_name', 'Unknown')}")
                return results
            else:
                logger.warning("没有有效的交易日期")
                return {}

        except Exception as e:
            logger.error(f"回测失败: {e}")
            return {}

    def _execute_trades(self, signals: Dict[str, str], daily_data: Dict, date):
        """执行交易"""
        try:
            for symbol, signal in signals.items():
                if symbol not in daily_data:
                    continue

                price = daily_data[symbol]['Close']
                current_shares = self.positions.get(symbol, 0)

                if signal == 'buy' and self.capital > price * 100:  # 最少买100股
                    # 计算买入数量（使用可用资金的一定比例）
                    max_investment = self.capital * RISK_CONTROL['max_position']
                    shares_to_buy = int(max_investment / price / 100) * 100  # 整百股

                    if shares_to_buy > 0:
                        cost = shares_to_buy * price
                        self.capital -= cost
                        self.positions[symbol] = current_shares + shares_to_buy

                        self.trades.append({
                            'date': date,
                            'symbol': symbol,
                            'action': 'buy',
                            'shares': shares_to_buy,
                            'price': price,
                            'cost': cost
                        })

                elif signal == 'sell' and current_shares > 0:
                    # 卖出所有持仓
                    proceeds = current_shares * price
                    self.capital += proceeds
                    self.positions[symbol] = 0

                    self.trades.append({
                        'date': date,
                        'symbol': symbol,
                        'action': 'sell',
                        'shares': current_shares,
                        'price': price,
                        'proceeds': proceeds
                    })

        except Exception as e:
            logger.warning(f"执行交易失败: {e}")

    def _calculate_portfolio_value(self, daily_data: Dict) -> float:
        """计算组合价值"""
        total_value = self.capital

        for symbol, shares in self.positions.items():
            if shares > 0 and symbol in daily_data:
                total_value += shares * daily_data[symbol]['Close']

        return total_value

    def _update_drawdown(self, current_value: float):
        """更新回撤"""
        if not self.portfolio_values:
            self.peak_value = current_value
        else:
            self.peak_value = max(self.peak_value, current_value)

        self.current_drawdown = (self.peak_value - current_value) / self.peak_value
        self.max_drawdown = max(self.max_drawdown, self.current_drawdown)

    def _calculate_backtest_results(self, start_date, end_date) -> Dict:
        """计算回测结果"""
        try:
            if not self.portfolio_values:
                return {}

            # 转换为DataFrame
            portfolio_df = pd.DataFrame(self.portfolio_values)

            # 确保date列是datetime类型
            if 'date' in portfolio_df.columns:
                portfolio_df['date'] = pd.to_datetime(portfolio_df['date'])
                portfolio_df.set_index('date', inplace=True)
            else:
                logger.error("portfolio_values中缺少date列")
                return {}

            # 计算收益率
            portfolio_df['returns'] = portfolio_df['value'].pct_change()

            # 基本统计
            if len(portfolio_df) > 0:
                final_value = portfolio_df['value'].iloc[-1]
                total_return = (final_value - self.initial_capital) / self.initial_capital

                # 避免除零错误
                if len(portfolio_df) > 1:
                    annual_return = (1 + total_return) ** (252 / len(portfolio_df)) - 1
                    volatility = portfolio_df['returns'].std() * np.sqrt(252)
                    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
                else:
                    annual_return = 0
                    volatility = 0
                    sharpe_ratio = 0
            else:
                total_return = 0
                annual_return = 0
                volatility = 0
                sharpe_ratio = 0

            # 胜率统计
            winning_trades = [t for t in self.trades if t['action'] == 'sell']
            if winning_trades:
                profits = []
                for i, sell_trade in enumerate(winning_trades):
                    # 找到对应的买入交易
                    buy_trades = [t for t in self.trades if
                                t['symbol'] == sell_trade['symbol'] and
                                t['action'] == 'buy' and
                                t['date'] < sell_trade['date']]
                    if buy_trades:
                        buy_price = buy_trades[-1]['price']  # 最近的买入价格
                        profit = (sell_trade['price'] - buy_price) / buy_price
                        profits.append(profit)

                win_rate = len([p for p in profits if p > 0]) / len(profits) if profits else 0
                avg_profit = np.mean(profits) if profits else 0
            else:
                win_rate = 0
                avg_profit = 0

            results = {
                'strategy_name': getattr(self, 'strategy_name', 'Unknown'),
                'start_date': start_date.strftime('%Y-%m-%d') if hasattr(start_date, 'strftime') else str(start_date),
                'end_date': end_date.strftime('%Y-%m-%d') if hasattr(end_date, 'strftime') else str(end_date),
                'initial_capital': self.initial_capital,
                'final_value': float(portfolio_df['value'].iloc[-1]) if not portfolio_df.empty else self.initial_capital,
                'total_return': float(total_return) if not np.isnan(total_return) else 0.0,
                'annual_return': float(annual_return) if not np.isnan(annual_return) else 0.0,
                'volatility': float(volatility) if not np.isnan(volatility) else 0.0,
                'sharpe_ratio': float(sharpe_ratio) if not np.isnan(sharpe_ratio) else 0.0,
                'max_drawdown': float(self.max_drawdown) if not np.isnan(self.max_drawdown) else 0.0,
                'win_rate': float(win_rate) if not np.isnan(win_rate) else 0.0,
                'avg_profit': float(avg_profit) if not np.isnan(avg_profit) else 0.0,
                'total_trades': len(self.trades),
                'portfolio_values': portfolio_df.reset_index().to_dict('records'),
                'trades': self.trades
            }

            return results

        except Exception as e:
            logger.error(f"计算回测结果失败: {e}")
            return {}

    def plot_backtest_results(self, results: Dict, save_path: str = None):
        """绘制回测结果"""
        try:
            if not results or 'portfolio_values' not in results:
                logger.warning("无回测结果可绘制")
                return

            portfolio_df = pd.DataFrame(results['portfolio_values'])
            portfolio_df['date'] = pd.to_datetime(portfolio_df['date'])
            portfolio_df.set_index('date', inplace=True)

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

            # 1. 组合价值曲线
            ax1.plot(portfolio_df.index, portfolio_df['value'], label='组合价值', linewidth=2)
            ax1.axhline(y=self.initial_capital, color='r', linestyle='--', label='初始资金')
            ax1.set_title(f'组合价值曲线 - {results["strategy_name"]}')
            ax1.set_ylabel('价值 (元)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 2. 收益率分布
            returns = portfolio_df['value'].pct_change().dropna()
            ax2.hist(returns, bins=50, alpha=0.7, color='skyblue')
            ax2.axvline(x=returns.mean(), color='r', linestyle='--', label=f'平均收益率: {returns.mean():.4f}')
            ax2.set_title('日收益率分布')
            ax2.set_xlabel('收益率')
            ax2.set_ylabel('频次')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 3. 回撤曲线
            cumulative_returns = (portfolio_df['value'] / self.initial_capital - 1)
            peak = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - peak)
            ax3.fill_between(drawdown.index, drawdown, 0, alpha=0.3, color='red')
            ax3.plot(drawdown.index, drawdown, color='red', linewidth=1)
            ax3.set_title(f'回撤曲线 (最大回撤: {results["max_drawdown"]:.2%})')
            ax3.set_ylabel('回撤')
            ax3.grid(True, alpha=0.3)

            # 4. 关键指标表
            ax4.axis('off')
            metrics_text = f"""
            策略名称: {results['strategy_name']}
            总收益率: {results['total_return']:.2%}
            年化收益率: {results['annual_return']:.2%}
            波动率: {results['volatility']:.2%}
            夏普比率: {results['sharpe_ratio']:.2f}
            最大回撤: {results['max_drawdown']:.2%}
            胜率: {results['win_rate']:.2%}
            平均盈利: {results['avg_profit']:.2%}
            交易次数: {results['total_trades']}
            """
            ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes, fontsize=12,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgray'))

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"回测结果图表已保存到: {save_path}")

            plt.show()

        except Exception as e:
            logger.error(f"绘制回测结果失败: {e}")

# 示例策略函数
def ma_crossover_strategy(daily_data: Dict, historical_data: Dict, params: Dict) -> Dict[str, str]:
    """移动平均线交叉策略"""
    signals = {}

    short_period = params.get('short_period', 5)
    long_period = params.get('long_period', 20)

    for symbol in daily_data.keys():
        if symbol not in historical_data:
            continue

        data = historical_data[symbol]
        if len(data) < long_period:
            continue

        # 计算移动平均线
        short_ma = data['Close'].rolling(window=short_period).mean()
        long_ma = data['Close'].rolling(window=long_period).mean()

        # 获取最近两天的MA值
        if len(short_ma) >= 2 and len(long_ma) >= 2:
            short_ma_current = short_ma.iloc[-1]
            short_ma_prev = short_ma.iloc[-2]
            long_ma_current = long_ma.iloc[-1]
            long_ma_prev = long_ma.iloc[-2]

            # 金叉买入信号
            if (short_ma_prev <= long_ma_prev and short_ma_current > long_ma_current):
                signals[symbol] = 'buy'
            # 死叉卖出信号
            elif (short_ma_prev >= long_ma_prev and short_ma_current < long_ma_current):
                signals[symbol] = 'sell'
            else:
                signals[symbol] = 'hold'

    return signals
