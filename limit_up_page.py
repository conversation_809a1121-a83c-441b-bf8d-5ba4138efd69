"""
封板预测页面
"""

import streamlit as st
import pandas as pd
import numpy as np
import akshare as ak
import logging
from datetime import datetime, timedelta, time
import time

logger = logging.getLogger(__name__)

def get_time_window_adjustment(current_time):
    """根据时间窗口调整预测准确性"""
    try:
        # 定义时间窗口
        optimal_start = time(9, 45)  # 最佳开始时间
        optimal_end = time(10, 30)   # 最佳结束时间
        early_start = time(9, 30)    # 早期开始
        late_morning = time(11, 30)  # 上午结束
        afternoon_start = time(13, 0) # 下午开始
        trading_end = time(15, 0)    # 交易结束

        # 最佳时间窗口 (9:45-10:30)
        if optimal_start <= current_time <= optimal_end:
            return 1.0  # 100% 准确性

        # 早期观察期 (9:30-9:45)
        elif early_start <= current_time < optimal_start:
            # 线性增长从70%到100%
            minutes_passed = (current_time.hour - 9) * 60 + current_time.minute - 30
            return 0.7 + (minutes_passed / 15) * 0.3

        # 次佳时间 (10:30-11:30)
        elif optimal_end < current_time <= late_morning:
            # 从100%线性下降到85%
            minutes_passed = (current_time.hour - 10) * 60 + current_time.minute - 30
            return 1.0 - (minutes_passed / 60) * 0.15

        # 下午时段 (13:00-15:00)
        elif afternoon_start <= current_time <= trading_end:
            return 0.6  # 60% 准确性

        # 非交易时间
        else:
            return 0.5  # 50% 准确性（历史数据）

    except Exception as e:
        logger.warning(f"时间窗口调整失败: {e}")
        return 1.0  # 默认不调整

def limit_up_prediction_page(components):
    """封板预测页面"""
    st.header("🚀 封板预测分析")
    st.markdown("---")

    # 数据准确性说明
    with st.expander("📊 数据准确性说明", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            st.write("**✅ 100%准确的数据:**")
            st.write("• 股票价格、涨跌幅")
            st.write("• 成交量、成交额")
            st.write("• 换手率、市值")
            st.write("• 技术指标计算")
            st.write("• 距离涨停计算")

        with col2:
            st.write("**⚠️ 参考性数据:**")
            st.write("• 主力资金流向 (80-90%准确)")
            st.write("• 大单监控 (依赖接口稳定性)")
            st.write("• 资金流向趋势 (算法估算)")

        st.info("💡 **重要提醒**: 封板预测仅供参考，不构成投资建议。投资有风险，决策需谨慎！")

        # 可靠性图例
        st.write("**🎯 数据可靠性图例:**")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.write("🟢 高可靠性 (80%+)")
        with col2:
            st.write("🟡 中等可靠性 (60-80%)")
        with col3:
            st.write("🔴 低可靠性 (<60%)")

    # 功能说明
    st.info("""
    💡 **封板预测功能说明**

    基于以下数据进行主板股票封板概率分析：
    - 📊 **昨日表现**: 涨跌幅、收盘位置、成交量等
    - 🌅 **今日开盘**: 高开幅度、当前涨幅、盘中强度
    - 💰 **资金流向**: 主力资金净流入、大单活跃度
    - 🎯 **综合评分**: 多维度加权计算封板概率
    """)

    # 分析模式选择
    st.subheader("⚙️ 分析设置")

    col1, col2, col3 = st.columns(3)

    with col1:
        analysis_mode = st.selectbox(
            "分析模式",
            ["智能扫描", "单股分析", "行业分析"],
            help="选择不同的分析模式"
        )

    with col2:
        min_probability = st.slider(
            "最低封板概率",
            min_value=30,
            max_value=80,
            value=50,
            step=5,
            help="筛选封板概率阈值"
        )

    with col3:
        max_results = st.selectbox(
            "结果数量",
            [10, 15, 20, 30],
            index=1,
            help="显示结果的最大数量"
        )

    st.markdown("---")

    if analysis_mode == "智能扫描":
        smart_scan_analysis(min_probability, max_results)
    elif analysis_mode == "单股分析":
        single_stock_analysis()
    elif analysis_mode == "行业分析":
        industry_analysis(components)

def smart_scan_analysis(min_probability, max_results):
    """智能扫描分析"""
    st.subheader("🔍 智能扫描潜在封板股票")

    # 时间窗口提醒
    current_time = datetime.now().time()
    trading_start = time(9, 30)
    trading_end = time(15, 0)
    optimal_start = time(9, 45)
    optimal_end = time(10, 30)

    if trading_start <= current_time <= trading_end:
        if optimal_start <= current_time <= optimal_end:
            st.success("✅ **最佳预测时间窗口** (9:45-10:30) - 预测准确性最高")
        elif time(9, 30) <= current_time < time(9, 45):
            st.warning("⚠️ **观察期** (9:30-9:45) - 建议等待数据稳定后再预测")
        elif time(10, 30) < current_time <= time(11, 30):
            st.info("💡 **次佳时间** (10:30-11:30) - 可预测但机会可能已错过")
        elif time(13, 0) <= current_time <= time(15, 0):
            st.warning("⚠️ **下午时段** - 封板概率相对较低")
    else:
        st.error("❌ **非交易时间** - 显示前一交易日数据，仅供参考")

    # 时间窗口说明
    with st.expander("⏰ 封板预测最佳时间窗口", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            st.write("**🎯 推荐时间窗口:**")
            st.write("• **9:45-10:30**: 最佳预测时间")
            st.write("• **10:30-11:30**: 次佳预测时间")
            st.write("• **9:30-9:45**: 观察期，不建议预测")

        with col2:
            st.write("**📊 时间窗口特点:**")
            st.write("• 开盘15分钟后数据趋于稳定")
            st.write("• 主力资金意图初步显现")
            st.write("• 技术形态基本确立")

    if st.button("🚀 开始扫描", type="primary"):
        with st.spinner("正在扫描潜在封板股票..."):
            try:
                # 获取实时数据
                realtime_data = ak.stock_zh_a_spot_em()

                if realtime_data.empty:
                    st.error("❌ 未获取到实时数据")
                    return

                # 筛选主板股票
                mainboard_data = realtime_data[
                    (realtime_data['代码'].str.startswith('600')) |
                    (realtime_data['代码'].str.startswith('601')) |
                    (realtime_data['代码'].str.startswith('603')) |
                    (realtime_data['代码'].str.startswith('605')) |
                    (realtime_data['代码'].str.startswith('000')) |
                    (realtime_data['代码'].str.startswith('001'))
                ]

                # 筛选候选股票：涨幅2-9%，成交额>5000万，换手率>1%
                candidates = mainboard_data[
                    (mainboard_data['涨跌幅'] > 2) &
                    (mainboard_data['涨跌幅'] < 9.5) &
                    (mainboard_data['成交额'] > 5e7) &
                    (mainboard_data['换手率'] > 1) &
                    (mainboard_data['最新价'] > 3)
                ].copy()

                if candidates.empty:
                    st.warning("⚠️ 当前市场条件下未找到符合条件的候选股票")
                    return

                st.info(f"📊 找到 {len(candidates)} 只候选股票，正在进行详细分析...")

                # 分析候选股票
                results = []
                progress_bar = st.progress(0)

                for i, (_, stock) in enumerate(candidates.head(50).iterrows()):
                    try:
                        # 计算封板概率 - 使用高级分析包含主力资金
                        prediction = calculate_advanced_limit_up_probability(stock, enable_fund_analysis=True)

                        if prediction and prediction['limit_up_probability'] >= min_probability:
                            results.append(prediction)

                        # 更新进度
                        progress_bar.progress((i + 1) / min(50, len(candidates)))

                    except Exception as e:
                        logger.warning(f"分析股票 {stock['代码']} 失败: {e}")
                        continue

                progress_bar.empty()

                if results:
                    # 按封板概率排序
                    results_df = pd.DataFrame(results)
                    results_df = results_df.sort_values('limit_up_probability', ascending=False).head(max_results)

                    display_scan_results(results_df)
                else:
                    st.warning(f"⚠️ 未找到封板概率≥{min_probability}%的股票")

            except Exception as e:
                st.error(f"❌ 扫描失败: {str(e)}")

def calculate_advanced_limit_up_probability(stock, enable_fund_analysis=True):
    """高级封板概率计算 - 包含主力资金分析"""
    try:
        # 数据验证和清洗
        stock_code = str(stock['代码']).strip()
        stock_name = str(stock['名称']).strip()

        # 价格数据验证
        try:
            current_price = float(stock['最新价'])
            open_price = float(stock['今开'])
            high_price = float(stock['最高'])
            low_price = float(stock['最低'])
            yesterday_close = float(stock['昨收'])
            current_change_pct = float(stock['涨跌幅'])
            volume = float(stock['成交量'])
            turnover_amount = float(stock['成交额'])
            turnover_rate = float(stock['换手率'])
        except (ValueError, TypeError) as e:
            logger.error(f"股票 {stock_code} 价格数据转换失败: {e}")
            return None

        # 数据合理性验证
        if current_price <= 0 or yesterday_close <= 0:
            logger.error(f"股票 {stock_code} 价格数据异常: 当前价{current_price}, 昨收{yesterday_close}")
            return None

        if turnover_amount < 0 or volume < 0:
            logger.error(f"股票 {stock_code} 成交数据异常: 成交额{turnover_amount}, 成交量{volume}")
            return None

        # 计算关键指标 - 确保计算准确性
        gap_pct = (open_price - yesterday_close) / yesterday_close * 100
        intraday_strength = (current_price - low_price) / (high_price - low_price) if high_price != low_price else 0.5

        # 精确计算涨停价格 (四舍五入到分)
        limit_up_price = round(yesterday_close * 1.1, 2)
        distance_to_limit = (limit_up_price - current_price) / yesterday_close * 100

        # 验证涨跌幅计算准确性
        calculated_change_pct = (current_price - yesterday_close) / yesterday_close * 100
        change_pct_diff = abs(calculated_change_pct - current_change_pct)

        if change_pct_diff > 0.1:  # 如果差异超过0.1%，记录警告
            logger.warning(f"股票 {stock_code} 涨跌幅计算差异: 计算值{calculated_change_pct:.2f}%, 数据源{current_change_pct:.2f}%")
            # 使用数据源的涨跌幅，但记录差异

        # 数据一致性检查
        if abs(distance_to_limit) > 15:  # 距离涨停超过15%的异常情况
            logger.warning(f"股票 {stock_code} 距离涨停异常: {distance_to_limit:.2f}%")

        # 评分计算 - 重新调整权重以包含资金流向
        score = 0
        fund_flow_info = {}

        # 1. 高开幅度评分 (20% - 降低权重)
        if gap_pct > 5:
            score += 20
        elif gap_pct > 3:
            score += 16
        elif gap_pct > 1:
            score += 12
        elif gap_pct > 0:
            score += 8

        # 2. 当前涨幅评分 (25% - 降低权重)
        if current_change_pct > 8:
            score += 25
        elif current_change_pct > 6:
            score += 20
        elif current_change_pct > 4:
            score += 16
        elif current_change_pct > 2:
            score += 12

        # 3. 盘中强度评分 (15% - 降低权重)
        if intraday_strength > 0.8:
            score += 15
        elif intraday_strength > 0.6:
            score += 12
        elif intraday_strength > 0.4:
            score += 8

        # 4. 成交活跃度评分 (10% - 降低权重)
        if turnover_rate > 8:
            score += 10
        elif turnover_rate > 5:
            score += 8
        elif turnover_rate > 3:
            score += 5

        # 5. 距离涨停评分 (10%)
        if distance_to_limit < 1:
            score += 10
        elif distance_to_limit < 2:
            score += 8
        elif distance_to_limit < 3:
            score += 6
        elif distance_to_limit < 5:
            score += 4

        # 6. 主力资金分析 (20% - 新增核心功能)
        if enable_fund_analysis:
            fund_score, fund_info = analyze_main_fund_flow(stock_code, turnover_amount, turnover_rate)
            score += fund_score
            fund_flow_info = fund_info

        # 转换为概率 - 根据时间窗口调整
        probability = min(score, 100)

        # 时间窗口调整因子
        current_time = datetime.now().time()
        time_adjustment = get_time_window_adjustment(current_time)
        probability = probability * time_adjustment

        # 风险等级
        if probability >= 80:
            risk_level = "极高概率"
            recommendation = "重点关注，主力资金强力推动"
        elif probability >= 65:
            risk_level = "高概率"
            recommendation = "积极关注，资金面配合良好"
        elif probability >= 50:
            risk_level = "中等概率"
            recommendation = "谨慎关注，存在封板机会"
        elif probability >= 35:
            risk_level = "较低概率"
            recommendation = "观望为主"
        else:
            risk_level = "低概率"
            recommendation = "不建议关注"

        result = {
            'stock_code': stock_code,
            'stock_name': stock_name,
            'limit_up_probability': probability,
            'risk_level': risk_level,
            'recommendation': recommendation,
            'current_price': current_price,
            'current_change_pct': current_change_pct,
            'distance_to_limit_up': distance_to_limit,
            'gap_pct': gap_pct,
            'intraday_strength': intraday_strength,
            'turnover_rate': turnover_rate,
            'industry': stock.get('所属行业', '未知')
        }

        # 添加资金流向信息
        if fund_flow_info:
            result.update(fund_flow_info)

        return result

    except Exception as e:
        logger.error(f"计算股票 {stock.get('代码', 'unknown')} 封板概率失败: {e}")
        return None

def analyze_main_fund_flow(stock_code, turnover_amount, turnover_rate):
    """分析主力资金流向 - 增强准确性"""
    try:
        import akshare as ak

        fund_score = 0
        fund_info = {
            'main_fund_inflow': 0,
            'main_fund_inflow_pct': 0,
            'fund_flow_trend': 'unknown',
            'big_deal_count': 0,
            'fund_analysis_success': False,
            'data_source': 'unknown',
            'reliability_score': 0
        }

        # 数据源可靠性评估
        reliability_score = 50  # 基础分

        # 1. 获取个股资金流向数据 - 多重验证
        try:
            market = 'sz' if stock_code.startswith('0') else 'sh'
            fund_flow = ak.stock_individual_fund_flow(stock=stock_code, market=market)

            if not fund_flow.empty:
                latest_flow = fund_flow.iloc[-1]
                main_inflow = float(latest_flow.get('主力净流入', 0))
                main_inflow_pct = float(latest_flow.get('主力净流入占比', 0))

                # 数据合理性验证
                if abs(main_inflow_pct) > 100:
                    logger.warning(f"股票 {stock_code} 主力净流入占比异常: {main_inflow_pct}%")
                    reliability_score -= 20

                # 数据一致性检查
                if abs(main_inflow) > turnover_amount:
                    logger.warning(f"股票 {stock_code} 主力净流入超过成交额: {main_inflow/1e8:.2f}亿 vs {turnover_amount/1e8:.2f}亿")
                    reliability_score -= 15

                fund_info.update({
                    'main_fund_inflow': main_inflow,
                    'main_fund_inflow_pct': main_inflow_pct,
                    'fund_analysis_success': True,
                    'data_source': 'akshare_api',
                    'reliability_score': reliability_score + 30  # API数据加分
                })

                # 主力净流入评分 (60%权重)
                if main_inflow > 1e8:  # 超过1亿
                    fund_score += 12
                    fund_info['fund_flow_trend'] = 'massive_inflow'
                elif main_inflow > 5e7:  # 超过5000万
                    fund_score += 10
                    fund_info['fund_flow_trend'] = 'strong_inflow'
                elif main_inflow > 2e7:  # 超过2000万
                    fund_score += 8
                    fund_info['fund_flow_trend'] = 'inflow'
                elif main_inflow > 0:
                    fund_score += 5
                    fund_info['fund_flow_trend'] = 'weak_inflow'
                elif main_inflow > -2e7:
                    fund_score += 2
                    fund_info['fund_flow_trend'] = 'neutral'
                else:
                    fund_score += 0
                    fund_info['fund_flow_trend'] = 'outflow'

                # 主力净流入占比评分 (40%权重)
                if main_inflow_pct > 15:
                    fund_score += 8
                elif main_inflow_pct > 10:
                    fund_score += 6
                elif main_inflow_pct > 5:
                    fund_score += 4
                elif main_inflow_pct > 0:
                    fund_score += 2

        except Exception as e:
            logger.warning(f"获取股票 {stock_code} 资金流向数据失败: {e}")

        # 2. 分析大单活跃度
        try:
            # 构造股票代码
            if stock_code.startswith('0') or stock_code.startswith('3'):
                symbol = f'sz{stock_code}'
            else:
                symbol = f'sh{stock_code}'

            # 获取实时大单数据
            big_deals = ak.stock_zh_a_tick_tx_js(symbol=symbol)

            if not big_deals.empty:
                big_deal_count = len(big_deals)
                fund_info['big_deal_count'] = big_deal_count

                # 大单活跃度加分
                if big_deal_count > 100:
                    fund_score += 3
                elif big_deal_count > 50:
                    fund_score += 2
                elif big_deal_count > 20:
                    fund_score += 1

        except Exception as e:
            logger.warning(f"获取股票 {stock_code} 大单数据失败: {e}")

        # 3. 基于成交额和换手率的资金活跃度评估
        if turnover_amount > 2e8:  # 超过2亿成交额
            fund_score += 2
        elif turnover_amount > 1e8:  # 超过1亿成交额
            fund_score += 1

        if turnover_rate > 10:  # 换手率超过10%
            fund_score += 2
        elif turnover_rate > 5:  # 换手率超过5%
            fund_score += 1

        return min(fund_score, 20), fund_info  # 最大20分

    except Exception as e:
        logger.error(f"分析股票 {stock_code} 主力资金失败: {e}")
        return 0, {'fund_analysis_success': False, 'error': str(e)}

# 保持向后兼容
def calculate_simple_limit_up_probability(stock):
    """简化版本 - 向后兼容"""
    return calculate_advanced_limit_up_probability(stock, enable_fund_analysis=False)

def display_scan_results(results_df):
    """显示扫描结果"""
    st.success(f"✅ 找到 {len(results_df)} 只潜在封板股票")

    # 统计信息
    col1, col2, col3, col4 = st.columns(4)

    high_prob = results_df[results_df['limit_up_probability'] >= 65]
    medium_prob = results_df[(results_df['limit_up_probability'] >= 50) & (results_df['limit_up_probability'] < 65)]
    rising_stocks = results_df[results_df['current_change_pct'] > 0]
    close_to_limit = results_df[results_df['distance_to_limit_up'] < 3]

    with col1:
        st.metric("🔥 高概率", len(high_prob), f"{len(high_prob)/len(results_df)*100:.0f}%")
    with col2:
        st.metric("⭐ 中等概率", len(medium_prob), f"{len(medium_prob)/len(results_df)*100:.0f}%")
    with col3:
        st.metric("📈 当日上涨", len(rising_stocks), f"{len(rising_stocks)/len(results_df)*100:.0f}%")
    with col4:
        st.metric("🎯 接近涨停", len(close_to_limit), f"{len(close_to_limit)/len(results_df)*100:.0f}%")

    # 详细结果表格
    st.subheader("🏆 封板概率排行榜")

    # 显示当前时间窗口状态
    current_time = datetime.now()
    time_adjustment = get_time_window_adjustment(current_time.time())

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("⏰ 当前时间", current_time.strftime("%H:%M:%S"))
    with col2:
        st.metric("🎯 预测准确性", f"{time_adjustment*100:.0f}%")
    with col3:
        if time_adjustment >= 0.9:
            window_status = "🟢 最佳时间"
        elif time_adjustment >= 0.8:
            window_status = "🟡 次佳时间"
        elif time_adjustment >= 0.6:
            window_status = "🟠 可用时间"
        else:
            window_status = "🔴 非最佳时间"
        st.metric("📊 时间窗口", window_status)

    display_data = []
    for i, (_, stock) in enumerate(results_df.iterrows(), 1):
        probability = stock['limit_up_probability']

        # 根据概率设置emoji
        if probability >= 80:
            emoji = "🔥🔥"
        elif probability >= 65:
            emoji = "🔥"
        elif probability >= 50:
            emoji = "⭐"
        else:
            emoji = "📈"

        # 添加主力资金信息 - 包含可靠性指示
        fund_info = ""
        if stock.get('fund_analysis_success', False):
            main_inflow = stock.get('main_fund_inflow', 0)
            reliability = stock.get('reliability_score', 0)

            # 资金流入等级
            if main_inflow > 1e8:
                fund_level = "💰 巨额流入"
            elif main_inflow > 5e7:
                fund_level = "💵 大额流入"
            elif main_inflow > 2e7:
                fund_level = "💸 资金流入"
            elif main_inflow > 0:
                fund_level = "💳 小额流入"
            else:
                fund_level = "📉 资金流出"

            # 可靠性指示
            if reliability >= 80:
                reliability_icon = "🟢"
            elif reliability >= 60:
                reliability_icon = "🟡"
            else:
                reliability_icon = "🔴"

            fund_info = f"{fund_level} {reliability_icon}"
        else:
            fund_info = "❓ 数据缺失"

        display_data.append({
            '排名': i,
            '股票': f"{emoji} {stock['stock_name']}",
            '代码': stock['stock_code'],
            '封板概率': f"{probability:.1f}%",
            '风险等级': stock['risk_level'],
            '当前价格': f"{stock['current_price']:.2f}元",
            '当前涨幅': f"{stock['current_change_pct']:+.2f}%",
            '距离涨停': f"{stock['distance_to_limit_up']:.2f}%",
            '换手率': f"{stock['turnover_rate']:.2f}%",
            '主力资金': fund_info,
            '行业': stock['industry'],
            '建议': stock['recommendation']
        })

    display_df = pd.DataFrame(display_data)
    st.dataframe(display_df, use_container_width=True)

    # 投资建议
    st.subheader("💡 封板预测投资建议")

    recommendations = []

    if not high_prob.empty:
        top_3_high = high_prob.head(3)
        top_names = ', '.join(top_3_high['stock_name'].tolist())
        recommendations.append(f"🎯 **重点关注**: {top_names} - 封板概率≥65%，具备较强封板潜力")

    if not close_to_limit.empty:
        close_count = len(close_to_limit)
        recommendations.append(f"⚡ **临界关注**: {close_count}只股票距离涨停<3%，密切监控突破情况")

    if not rising_stocks.empty:
        rising_count = len(rising_stocks)
        recommendations.append(f"📈 **趋势向好**: {rising_count}只股票当日上涨，市场情绪积极")

    recommendations.extend([
        "🚀 **操作策略**: 开盘30分钟内是关键观察期，注意成交量放大",
        "⚠️ **风险控制**: 涨停板风险极高，严格控制单只股票仓位≤2%",
        "💡 **时机把握**: 建议在股票接近涨停前1-2%时关注，避免追高"
    ])

    for i, rec in enumerate(recommendations, 1):
        st.write(f"{i}. {rec}")

    # 风险提示
    st.warning("""
    ⚠️ **重要风险提示**

    - 封板预测仅供参考，不构成投资建议
    - 市场变化快速，预测结果可能随时失效
    - 涨停板投资风险极高，可能面临巨大损失
    - 请严格控制仓位，设置止损位
    - 理性投资，量力而行
    """)

def single_stock_analysis():
    """单股分析"""
    st.subheader("🎯 单只股票封板分析")

    stock_code = st.text_input(
        "请输入主板股票代码",
        placeholder="例如: 000001, 600519, 000002",
        help="仅支持主板股票（60xxxx, 00xxxx）"
    )

    if st.button("🔍 分析封板概率") and stock_code:
        with st.spinner(f"正在分析股票 {stock_code} 的封板概率..."):
            try:
                # 获取股票实时数据
                realtime_data = ak.stock_zh_a_spot_em()
                stock_data = realtime_data[realtime_data['代码'] == stock_code]

                if stock_data.empty:
                    st.error(f"❌ 未找到股票代码 {stock_code}")
                    return

                stock = stock_data.iloc[0]
                prediction = calculate_advanced_limit_up_probability(stock, enable_fund_analysis=True)

                if prediction:
                    display_single_stock_result(prediction)
                else:
                    st.error("❌ 分析失败，请检查股票代码")

            except Exception as e:
                st.error(f"❌ 分析失败: {str(e)}")

def display_single_stock_result(prediction):
    """显示单股分析结果"""
    probability = prediction['limit_up_probability']

    # 根据概率设置颜色和emoji
    if probability >= 80:
        emoji = "🔥🔥"
        color = "red"
    elif probability >= 65:
        emoji = "🔥"
        color = "orange"
    elif probability >= 50:
        emoji = "⭐"
        color = "blue"
    else:
        emoji = "📈"
        color = "gray"

    st.markdown(f"### {emoji} {prediction['stock_name']} ({prediction['stock_code']})")

    # 核心指标
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("封板概率", f"{probability:.1f}%", help="基于多维度分析的封板概率")
    with col2:
        st.metric("风险等级", prediction['risk_level'])
    with col3:
        st.metric("当前价格", f"{prediction['current_price']:.2f}元")
    with col4:
        st.metric("当前涨幅", f"{prediction['current_change_pct']:+.2f}%")

    # 详细分析
    col1, col2 = st.columns(2)

    with col1:
        st.write("**📊 关键指标**")
        st.write(f"• 距离涨停: {prediction['distance_to_limit_up']:.2f}%")
        st.write(f"• 高开幅度: {prediction['gap_pct']:+.2f}%")
        st.write(f"• 盘中强度: {prediction['intraday_strength']:.1%}")
        st.write(f"• 换手率: {prediction['turnover_rate']:.2f}%")
        st.write(f"• 所属行业: {prediction['industry']}")

        # 显示主力资金信息
        if prediction.get('fund_analysis_success', False):
            st.write("**💰 主力资金分析**")
            main_inflow = prediction.get('main_fund_inflow', 0)
            main_inflow_pct = prediction.get('main_fund_inflow_pct', 0)
            fund_trend = prediction.get('fund_flow_trend', 'unknown')

            if main_inflow > 0:
                st.write(f"• 主力净流入: +{main_inflow/1e8:.2f}亿元")
            else:
                st.write(f"• 主力净流出: {main_inflow/1e8:.2f}亿元")

            st.write(f"• 流入占比: {main_inflow_pct:+.2f}%")

            trend_desc = {
                'massive_inflow': '🔥 巨额流入',
                'strong_inflow': '💪 强力流入',
                'inflow': '📈 资金流入',
                'weak_inflow': '💧 微量流入',
                'neutral': '⚖️ 资金平衡',
                'outflow': '📉 资金流出'
            }
            st.write(f"• 资金趋势: {trend_desc.get(fund_trend, '❓ 未知')}")
        else:
            st.write("**💰 主力资金分析**")
            st.write("• ⚠️ 资金数据获取失败")

    with col2:
        st.write("**💡 投资建议**")
        st.info(prediction['recommendation'])

        if probability >= 65:
            st.success("✅ 建议重点关注，具备较强封板潜力")
        elif probability >= 50:
            st.warning("⚠️ 谨慎关注，存在封板机会")
        else:
            st.info("💡 概率较低，建议观望")

def industry_analysis(components):
    """行业分析"""
    st.subheader("🏭 行业封板潜力分析")
    st.info("💡 分析各强势行业中最有封板潜力的主板股票")

    if st.button("🔍 分析行业封板潜力"):
        with st.spinner("正在分析行业封板潜力..."):
            try:
                # 获取强势行业
                performance_df = components['industry_analyzer'].calculate_industry_performance()
                signals = components['industry_analyzer'].detect_rotation_signals(performance_df)
                strong_industries = signals.get('strong_industries', [])

                if not strong_industries:
                    st.warning("⚠️ 当前未发现强势行业")
                    return

                st.success(f"✅ 发现 {len(strong_industries)} 个强势行业")

                # 分析每个强势行业的封板潜力
                industry_results = []

                for industry in strong_industries[:5]:  # 分析前5个强势行业
                    try:
                        industry_potential = analyze_industry_limit_up_potential(industry)
                        if industry_potential:
                            industry_results.append(industry_potential)
                    except Exception as e:
                        logger.warning(f"分析行业 {industry} 失败: {e}")
                        continue

                if industry_results:
                    display_industry_results(industry_results)
                else:
                    st.warning("⚠️ 未找到有封板潜力的行业")

            except Exception as e:
                st.error(f"❌ 行业分析失败: {str(e)}")

def analyze_industry_limit_up_potential(industry_name):
    """分析行业封板潜力"""
    try:
        # 获取行业成分股（简化版本）
        realtime_data = ak.stock_zh_a_spot_em()

        # 筛选主板股票
        mainboard_data = realtime_data[
            (realtime_data['代码'].str.startswith('600')) |
            (realtime_data['代码'].str.startswith('601')) |
            (realtime_data['代码'].str.startswith('603')) |
            (realtime_data['代码'].str.startswith('605')) |
            (realtime_data['代码'].str.startswith('000')) |
            (realtime_data['代码'].str.startswith('001'))
        ]

        # 简单筛选：涨幅>1%的股票作为该行业候选
        candidates = mainboard_data[
            (mainboard_data['涨跌幅'] > 1) &
            (mainboard_data['成交额'] > 2e7) &
            (mainboard_data['换手率'] > 0.5)
        ].head(10)  # 取前10只作为代表

        if candidates.empty:
            return None

        # 计算行业平均封板概率
        probabilities = []
        best_stock = None
        best_probability = 0

        for _, stock in candidates.iterrows():
            try:
                prediction = calculate_simple_limit_up_probability(stock)
                if prediction:
                    probabilities.append(prediction['limit_up_probability'])
                    if prediction['limit_up_probability'] > best_probability:
                        best_probability = prediction['limit_up_probability']
                        best_stock = prediction
            except:
                continue

        if not probabilities:
            return None

        avg_probability = np.mean(probabilities)
        high_potential_count = len([p for p in probabilities if p >= 60])

        # 行业评级
        if avg_probability >= 60 and high_potential_count >= 3:
            rating = "🔥 极强"
        elif avg_probability >= 50 and high_potential_count >= 2:
            rating = "⭐ 强势"
        elif avg_probability >= 40 and high_potential_count >= 1:
            rating = "📈 中等"
        else:
            rating = "📊 一般"

        return {
            'industry_name': industry_name,
            'average_probability': avg_probability,
            'high_potential_count': high_potential_count,
            'analyzed_stocks_count': len(probabilities),
            'best_stock': best_stock,
            'rating': rating
        }

    except Exception as e:
        logger.error(f"分析行业 {industry_name} 封板潜力失败: {e}")
        return None

def display_industry_results(industry_results):
    """显示行业分析结果"""
    st.subheader("🏆 行业封板潜力排行")

    # 按平均概率排序
    industry_results.sort(key=lambda x: x['average_probability'], reverse=True)

    for i, result in enumerate(industry_results, 1):
        with st.expander(f"{i}. {result['rating']} {result['industry_name']} (平均概率: {result['average_probability']:.1f}%)"):
            col1, col2 = st.columns(2)

            with col1:
                st.write("**📊 行业统计**")
                st.write(f"• 分析股票数: {result['analyzed_stocks_count']}只")
                st.write(f"• 平均封板概率: {result['average_probability']:.1f}%")
                st.write(f"• 高潜力股票: {result['high_potential_count']}只")
                st.write(f"• 行业评级: {result['rating']}")

            with col2:
                if result['best_stock']:
                    best = result['best_stock']
                    st.write("**🎯 最佳标的**")
                    st.write(f"• {best['stock_name']} ({best['stock_code']})")
                    st.write(f"• 封板概率: {best['limit_up_probability']:.1f}%")
                    st.write(f"• 当前涨幅: {best['current_change_pct']:+.2f}%")
                    st.write(f"• 距离涨停: {best['distance_to_limit_up']:.2f}%")
