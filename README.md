# 🚀 A股行业轮动分析系统

一个功能完整的A股市场行业轮动分析和股票技术分析系统，支持政策影响分析、龙头股票识别、技术指标计算和策略回测。

## ✨ 主要功能

### 🔄 行业轮动分析
- **实时行业表现监控**: 获取申万行业分类数据，计算各行业涨跌幅和强度评分
- **轮动信号检测**: 自动识别强势、弱势、轮动候选和防御性行业
- **可视化图表**: 生成行业表现排行榜、强度分布图等多维度图表
- **投资建议生成**: 基于分析结果提供具体的投资建议

### 📈 技术指标分析
- **多种技术指标**: MA、RSI、MACD、布林带、KDJ等经典技术指标
- **交易信号生成**: 基于技术指标组合生成买入/卖出/持有信号
- **支撑阻力位**: 自动计算关键价位支撑和阻力位
- **综合评分**: 多指标综合评估股票技术面强弱

### 📜 政策影响分析
- **政策新闻监控**: 自动获取和分析政策相关新闻
- **行业影响映射**: 将政策关键词映射到受影响的具体行业
- **影响评分**: 量化政策对不同行业的影响程度
- **受益行业识别**: 识别政策利好的重点受益行业

### 🏆 龙头股票识别
- **行业龙头配置**: 预配置各行业龙头股票池
- **实时数据获取**: 获取龙头股票的实时价格和财务数据
- **技术分析**: 对龙头股票进行深度技术分析
- **投资价值评估**: 综合基本面和技术面评估投资价值

### 🔬 策略回测
- **多策略支持**: 支持移动平均线交叉等多种交易策略
- **完整回测框架**: 包含资金管理、风险控制、交易执行等完整流程
- **性能评估**: 计算收益率、夏普比率、最大回撤等关键指标
- **可视化结果**: 生成收益曲线、回撤图等直观的回测结果图表

## 🛠️ 技术架构

### 核心模块
```
├── config.py              # 配置文件
├── data_fetcher.py         # 数据获取模块
├── industry_analyzer.py    # 行业轮动分析
├── technical_analyzer.py   # 技术指标分析
├── backtest_engine.py      # 回测引擎
├── main_app.py            # Streamlit主应用
└── run_analysis.py        # 快速分析脚本
```

### 数据源
- **AKShare**: 主要数据源，获取A股实时数据
- **YFinance**: 备用数据源
- **Tushare**: 可选数据源（需要token）

### 技术栈
- **Python 3.8+**
- **数据处理**: pandas, numpy
- **可视化**: matplotlib, seaborn, plotly
- **Web界面**: streamlit, dash
- **机器学习**: scikit-learn
- **数据获取**: akshare, yfinance, tushare

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd economic

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置
编辑 `config.py` 文件，根据需要调整配置：
```python
# 如果使用Tushare，请设置token
TUSHARE_TOKEN = "your_tushare_token_here"

# 调整行业分类和龙头股票配置
LEADING_STOCKS = {
    '新能源': {
        '比亚迪': '002594',
        '宁德时代': '300750',
        # ...
    }
}
```

### 3. 运行方式

#### 方式一：Web界面 (推荐)
```bash
streamlit run main_app.py
```
然后在浏览器中打开 `http://localhost:8501`

#### 方式二：命令行分析
```bash
# 完整分析
python run_analysis.py

# 快速行业分析
python run_analysis.py quick

# 分析指定股票
python run_analysis.py stocks 000001,600519,300750
```

## 📊 使用示例

### 行业轮动分析
```python
from industry_analyzer import IndustryAnalyzer

analyzer = IndustryAnalyzer()

# 生成完整的行业轮动报告
report = analyzer.generate_rotation_report()

# 获取行业表现数据
performance_df = analyzer.calculate_industry_performance()

# 检测轮动信号
signals = analyzer.detect_rotation_signals(performance_df)

print("强势行业:", signals['strong_industries'])
print("轮动候选:", signals['rotation_candidates'])
```

### 技术指标分析
```python
from technical_analyzer import TechnicalAnalyzer

analyzer = TechnicalAnalyzer()

# 分析单只股票
result = analyzer.analyze_stock_technical('000001')

print(f"当前价格: {result['current_price']}")
print(f"RSI: {result['indicators']['RSI']}")
print(f"综合信号: {result['signals']['overall_signal']}")
```

### 策略回测
```python
from backtest_engine import BacktestEngine, ma_crossover_strategy

# 创建回测引擎
engine = BacktestEngine(initial_capital=100000)
engine.add_strategy(ma_crossover_strategy, "MA交叉策略")

# 运行回测
results = engine.run_backtest(
    symbols=['000001', '600519'],
    start_date='2023-01-01',
    end_date='2024-01-01',
    strategy_params={'short_period': 5, 'long_period': 20}
)

print(f"总收益率: {results['total_return']:.2%}")
print(f"夏普比率: {results['sharpe_ratio']:.2f}")
```

## 📈 分析报告示例

### 当前市场概况
- **强势行业**: 新能源汽车、人工智能、医药生物
- **弱势行业**: 房地产、传统金融、煤炭
- **轮动机会**: 消费电子、新材料、军工

### 政策影响分析
- **新能源政策**: 利好光伏、风电、储能板块
- **AI政策**: 推动算力、芯片、软件行业发展
- **消费政策**: 促进内需，利好食品饮料、家电等

### 龙头股票推荐
| 行业 | 股票名称 | 代码 | 当前价格 | 技术信号 | RSI |
|------|----------|------|----------|----------|-----|
| 新能源 | 比亚迪 | 002594 | 245.30 | 🟢 bullish | 65.2 |
| AI | 科大讯飞 | 002230 | 58.76 | 🟡 neutral | 52.8 |
| 医药 | 恒瑞医药 | 600276 | 42.15 | 🔴 bearish | 35.4 |

## ⚙️ 配置说明

### 行业分类配置
在 `config.py` 中可以自定义行业分类：
```python
INDUSTRY_MAPPING = {
    '新能源': ['新能源汽车', '光伏', '风电', '储能'],
    '人工智能': ['AI芯片', '算力', '软件', '机器人'],
    # 添加更多行业...
}
```

### 技术指标参数
```python
TECHNICAL_INDICATORS = {
    'ma_periods': [5, 10, 20, 60],
    'rsi_period': 14,
    'macd_params': (12, 26, 9),
    'bollinger_period': 20
}
```

### 风险控制参数
```python
RISK_CONTROL = {
    'max_position': 0.1,    # 单只股票最大仓位10%
    'stop_loss': 0.08,      # 止损8%
    'take_profit': 0.15,    # 止盈15%
    'max_drawdown': 0.05    # 最大回撤5%
}
```

## 🔧 扩展开发

### 添加新的技术指标
```python
def calculate_custom_indicator(self, data: pd.Series) -> pd.Series:
    """自定义技术指标"""
    # 实现你的指标计算逻辑
    return result
```

### 添加新的交易策略
```python
def custom_strategy(daily_data, historical_data, params):
    """自定义交易策略"""
    signals = {}
    # 实现你的策略逻辑
    return signals
```

### 添加新的数据源
```python
def get_custom_data_source(self, symbol: str) -> pd.DataFrame:
    """自定义数据源"""
    # 实现数据获取逻辑
    return data
```

## 📝 注意事项

1. **数据源限制**: 免费数据源可能有访问频率限制，建议合理控制请求频率
2. **网络连接**: 确保网络连接稳定，数据获取依赖外部API
3. **数据准确性**: 数据仅供参考，投资决策请结合多方面信息
4. **风险提示**: 股市有风险，投资需谨慎

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**免责声明**: 本系统仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。
